{% extends "layouts/base.html" %}

{% block title %} Crowd Analytics & Monitoring {% endblock %}

{% block stylesheets %}
<style>
    .table th, .table td {
        text-align: center;
        vertical-align: middle;
    }

    .table thead {
        background-color: #f2f2f2;
        font-weight: bold;
    }

    .table th {
        padding: 10px;
        font-size: 14px;
    }

    .table td {
        padding: 10px;
        font-size: 12px;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .input-search {
        width: 100%;
        padding: 5px;
        margin-bottom: 10px;
    }

    .input-search:focus {
        border-color: #007bff;
    }

    .card-body {
        padding: 15px;
    }

    .card-header {
        background-color: #007bff;
        color: white;
        padding: 15px;
    }

    .camera-link {
        color: blue;
        text-decoration: underline;
        position: relative;
    }

    .camera-link:hover::after {
        content: "Click to view camera feed";
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background-color: #333;
        color: white;
        padding: 5px;
        border-radius: 3px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
    }

    /* Status indicators */
    .status-normal { background-color: #d4edda; color: #155724; }
    .status-anomaly { background-color: #fff3cd; color: #856404; }
    .status-weapon { background-color: #f8d7da; color: #721c24; }
    .status-entry { background-color: #e8f5e8; color: #2d5a2d; }
    .status-exit { background-color: #ffe8e8; color: #722d2d; }

    .loading-row {
        background-color: #f8f9fa;
        color: #6c757d;
        font-style: italic;
    }
    
    .no-data-row {
        background-color: #fff3cd;
        color: #856404;
    }

    .summary-card {
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .summary-card:hover {
        transform: translateY(-5px);
    }

    .summary-number {
        font-size: 2rem;
        font-weight: bold;
        color: #007bff;
    }

    .summary-label {
        font-size: 0.9rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .activity-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .activity-active { background-color: #28a745; }
    .activity-moderate { background-color: #ffc107; }
    .activity-low { background-color: #dc3545; }

    .movement-arrow {
        font-size: 1.2em;
        margin-right: 5px;
    }

    .arrow-up { color: #007bff; }
    .arrow-down { color: #28a745; }
    .arrow-left { color: #ffc107; }
    .arrow-right { color: #dc3545; }
    .arrow-stationary { color: #6c757d; }

    .speed-indicator {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: bold;
    }

    .speed-slow { background-color: #d4edda; color: #155724; }
    .speed-medium { background-color: #fff3cd; color: #856404; }
    .speed-fast { background-color: #f8d7da; color: #721c24; }

    .data-source-info {
        font-size: 0.8em;
        color: #6c757d;
        text-align: center;
        margin-top: 10px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }

    .refresh-indicator {
        display: inline-block;
        color: #28a745;
        margin-left: 10px;
        font-size: 0.9em;
    }

    .refresh-indicator.updating {
        animation: pulse 1s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
</style>
{% endblock stylesheets %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Summary Dashboard -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <div class="summary-number" id="totalPeopleCount">Loading...</div>
                    <div class="summary-label">Total People Detected</div>
                    <div class="mt-2">
                        <span class="activity-indicator activity-active" id="activityIndicator"></span>
                        <span id="activityStatus" class="text-muted">Analyzing...</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <div class="summary-number" id="activeTracksCount">Loading...</div>
                    <div class="summary-label">Active Tracks</div>
                    <div class="mt-2 text-muted" id="trackingDetails">Initializing...</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <div class="summary-number" id="totalEntries">Loading...</div>
                    <div class="summary-label">Total Entries</div>
                    <div class="mt-2">
                        <span class="text-success" id="entryExitRatio">Calculating...</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card summary-card">
                <div class="card-body text-center">
                    <div class="summary-number" id="anomalyCount">Loading...</div>
                    <div class="summary-label">Anomalies Detected</div>
                    <div class="mt-2 text-muted" id="anomalyDetails">Scanning...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Analytics Table -->
    <div class="row">
        <div class="col-12">
            <div class="card my-4">
                <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                    <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3">
                        <h6 class="text-white text-capitalize ps-3">
                            Real-Time Crowd Analytics & Movement Tracking
                            <span class="refresh-indicator" id="refreshIndicator">
                                <i class="fas fa-circle text-success"></i> Live
                            </span>
                        </h6>
                    </div>
                </div>
                <div class="card-body px-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0" id="analyticsTable">
                            <thead>
                                <tr>
                                    <th><input type="text" class="form-control input-search" placeholder="Search Time..." onkeyup="filterTable()"></th>
                                    <th><input type="text" class="form-control input-search" placeholder="Search Camera..." onkeyup="filterTable()"></th>
                                    <th><input type="text" class="form-control input-search" placeholder="Search Track..." onkeyup="filterTable()"></th>
                                    <th><input type="text" class="form-control input-search" placeholder="Search Direction..." onkeyup="filterTable()"></th>
                                    <th><input type="text" class="form-control input-search" placeholder="Search Status..." onkeyup="filterTable()"></th>
                                    <th><input type="text" class="form-control input-search" placeholder="Search Speed..." onkeyup="filterTable()"></th>
                                    <th><input type="text" class="form-control input-search" placeholder="Search Duration..." onkeyup="filterTable()"></th>
                                    <th></th>
                                </tr>
                                <tr>
                                    <th>Time</th>
                                    <th>Camera</th>
                                    <th>Track ID</th>
                                    <th>Movement</th>
                                    <th>Status</th>
                                    <th>Speed</th>
                                    <th>Duration</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="loading-row">
                                    <td colspan="8" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> Loading real-time tracking data...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="data-source-info">
                
                        <strong>Update Frequency:</strong> Every 2 seconds • 
                        <strong>Last Updated:</strong> <span id="lastUpdateTime">Initializing...</span>
                        <br>
                        <small>Tracking data includes person detection, movement analysis, and anomaly detection across 3 camera feeds</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let updateInterval;
let lastDataTimestamp = 0;

function filterTable() {
    const table = document.getElementById("analyticsTable");
    const tr = table.getElementsByTagName("tr");
    const inputs = document.querySelectorAll("thead input");

    for (let i = 2; i < tr.length; i++) {
        let match = true;

        for (let j = 0; j < inputs.length - 1; j++) { // -1 to exclude actions column
            const filter = inputs[j].value.toLowerCase();
            const td = tr[i].getElementsByTagName("td")[j];

            if (td && filter) {
                const txtValue = td.textContent || td.innerText;
                if (!txtValue.toLowerCase().includes(filter)) {
                    match = false;
                    break;
                }
            }
        }
        tr[i].style.display = match ? "" : "none";
    }
}

function getMovementArrow(direction) {
    const arrows = {
        'Up': '<span class="movement-arrow arrow-up">↑</span>',
        'Down': '<span class="movement-arrow arrow-down">↓</span>',
        'Left': '<span class="movement-arrow arrow-left">←</span>',
        'Right': '<span class="movement-arrow arrow-right">→</span>',
        'Stationary': '<span class="movement-arrow arrow-stationary">●</span>'
    };
    return arrows[direction] || '<span class="movement-arrow arrow-stationary">●</span>';
}

function getSpeedClass(speed) {
    if (speed < 5) return 'speed-slow';
    if (speed < 15) return 'speed-medium';
    return 'speed-fast';
}

function getSpeedLabel(speed) {
    if (speed < 5) return 'Slow';
    if (speed < 15) return 'Medium';
    return 'Fast';
}

function formatDuration(seconds) {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
}

function updateRefreshIndicator(isUpdating) {
    const indicator = document.getElementById('refreshIndicator');
    if (isUpdating) {
        indicator.className = 'refresh-indicator updating';
        indicator.innerHTML = '<i class="fas fa-sync fa-spin text-warning"></i> Updating...';
    } else {
        indicator.className = 'refresh-indicator';
        indicator.innerHTML = '<i class="fas fa-circle text-success"></i> Live';
    }
}

function updateAnalyticsTable() {
    updateRefreshIndicator(true);
    
    Promise.all([
        fetch('/json/tracking').then(response => {
            if (!response.ok) throw new Error(`Tracking API error: ${response.status}`);
            return response.json();
        }),
        fetch('/json/camera_stats').then(response => {
            if (!response.ok) throw new Error(`Camera stats API error: ${response.status}`);
            return response.json();
        })
    ])
    .then(([trackingData, cameraStats]) => {
        console.log('Analytics update:', {
            tracks: Object.keys(trackingData).length,
            cameraStats: cameraStats
        });
        
        updateSummaryCards(trackingData, cameraStats);
        updateTrackingTable(trackingData, cameraStats);
        
        document.getElementById('lastUpdateTime').textContent = new Date().toLocaleTimeString();
        updateRefreshIndicator(false);
    })
    .catch(error => {
        console.error("Error updating analytics:", error);
        showErrorState(error.message);
        updateRefreshIndicator(false);
    });
}

function updateSummaryCards(trackingData, cameraStats) {
    // Calculate metrics
    const totalPeople = Object.values(cameraStats).reduce((sum, cam) => sum + (cam.people_count || 0), 0);
    const totalEntries = Object.values(cameraStats).reduce((sum, cam) => sum + (cam.entries || 0), 0);
    const totalExits = Object.values(cameraStats).reduce((sum, cam) => sum + (cam.exits || 0), 0);
    const activeTracks = Object.keys(trackingData).length;
    
    // Count anomalies
    let anomalyCount = 0;
    for (const track of Object.values(trackingData)) {
        if (track.anomaly && track.anomaly !== 'Normal') {
            anomalyCount++;
        }
    }
    
    // Update summary cards
    document.getElementById('totalPeopleCount').textContent = totalPeople;
    document.getElementById('activeTracksCount').textContent = activeTracks;
    document.getElementById('totalEntries').textContent = totalEntries;
    document.getElementById('anomalyCount').textContent = anomalyCount;
    
    // Update activity status
    const activityIndicator = document.getElementById('activityIndicator');
    const activityStatus = document.getElementById('activityStatus');
    if (activeTracks > 10) {
        activityIndicator.className = 'activity-indicator activity-active';
        activityStatus.textContent = 'High Activity';
    } else if (activeTracks > 5) {
        activityIndicator.className = 'activity-indicator activity-moderate';
        activityStatus.textContent = 'Moderate Activity';
    } else {
        activityIndicator.className = 'activity-indicator activity-low';
        activityStatus.textContent = 'Low Activity';
    }
    
    // Update tracking details
    const currentTime = Date.now() / 1000;
    const recentTracks = Object.values(trackingData).filter(track => 
        (currentTime - (track.start_time + track.dwell_time)) < 30
    ).length;
    document.getElementById('trackingDetails').textContent = `${recentTracks} recent tracks`;
    
    // Update entry/exit ratio
    const netFlow = totalEntries - totalExits;
    document.getElementById('entryExitRatio').textContent = 
        `Net: ${netFlow > 0 ? '+' : ''}${netFlow} (${totalExits} exits)`;
    
    // Update anomaly details
    if (anomalyCount > 0) {
        document.getElementById('anomalyDetails').textContent = 'Requires attention';
        document.getElementById('anomalyDetails').className = 'mt-2 text-warning';
    } else {
        document.getElementById('anomalyDetails').textContent = 'All normal';
        document.getElementById('anomalyDetails').className = 'mt-2 text-success';
    }
}

function updateTrackingTable(trackingData, cameraStats) {
    const tableBody = document.querySelector("#analyticsTable tbody");
    tableBody.innerHTML = "";
    
    const cameraMapping = {
        "cam1": "Camera 1 - Main Entrance",
        "cam2": "Camera 2 - Cafeteria", 
        "cam3": "Camera 3 - Library"
    };
    
    // Convert to array and sort by most recent activity
    const tracksArray = Object.entries(trackingData).map(([id, track]) => ({
        id,
        ...track,
        lastActivity: track.start_time + track.dwell_time
    })).sort((a, b) => b.lastActivity - a.lastActivity);
    
    if (tracksArray.length === 0) {
        const row = document.createElement("tr");
        row.className = "no-data-row";
        row.innerHTML = `<td colspan="8" class="text-center">No active tracking data available</td>`;
        tableBody.appendChild(row);
        return;
    }
    
    // Show last 50 tracks for performance
    const recentTracks = tracksArray.slice(0, 50);
    
    recentTracks.forEach(track => {
        const startTime = new Date(track.start_time * 1000);
        const timeStr = startTime.toLocaleTimeString();
        
        const cameraName = cameraMapping[track.camera] || track.camera || "Unknown";
        const direction = track.movement_direction || "Stationary";
        const status = track.anomaly || "Normal";
        const speed = track.current_speed || 0;
        const duration = track.dwell_time || 0;
        
        // Determine row class based on status
        let rowClass = "";
        if (status.toLowerCase().includes("anomaly")) {
            rowClass = "status-anomaly";
        } else if (status.toLowerCase().includes("weapon")) {
            rowClass = "status-weapon";
        } else if (direction.toLowerCase().includes("entry")) {
            rowClass = "status-entry";
        } else if (direction.toLowerCase().includes("exit")) {
            rowClass = "status-exit";
        } else {
            rowClass = "status-normal";
        }
        
        const row = document.createElement("tr");
        row.className = rowClass;
        
        row.innerHTML = `
            <td>${timeStr}</td>
            <td>
                <a href="/cameras.html#${track.camera}" class="camera-link">${cameraName}</a>
            </td>
            <td>
                <strong>#${track.id}</strong>
                <br><small class="text-muted">${track.trajectory?.length || 0} points</small>
            </td>
            <td>
                ${getMovementArrow(direction)}${direction}
            </td>
            <td>
                <span class="badge ${status === 'Normal' ? 'bg-success' : 'bg-warning'}">${status}</span>
            </td>
            <td>
                <span class="speed-indicator ${getSpeedClass(speed)}">
                    ${speed.toFixed(1)} px/f
                </span>
                <br><small class="text-muted">${getSpeedLabel(speed)}</small>
            </td>
            <td>
                ${formatDuration(duration)}
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewTrackDetails('${track.id}')">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
    
    console.log(`Updated table with ${recentTracks.length} tracking entries`);
}

function viewTrackDetails(trackId) {
    // This would open a modal or navigate to a detailed view
    alert(`Viewing details for Track #${trackId}\n\nAction`);
}

function showErrorState(errorMessage) {
    const tableBody = document.querySelector("#analyticsTable tbody");
    tableBody.innerHTML = `
        <tr class="no-data-row">
            <td colspan="8" class="text-center">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <strong>Error loading data:</strong> ${errorMessage}
                <br>
                <small>Please check if the tracking system is running</small>
                <br>
                <button class="btn btn-sm btn-primary mt-2" onclick="updateAnalyticsTable()">
                    <i class="fas fa-refresh"></i> Retry
                </button>
            </td>
        </tr>
    `;
    
    // Reset summary cards to error state
    ['totalPeopleCount', 'activeTracksCount', 'totalEntries', 'anomalyCount'].forEach(id => {
        document.getElementById(id).textContent = 'Error';
    });
}

document.addEventListener("DOMContentLoaded", function(){
    console.log('Initializing real-time crowd analytics...');
    
    // Add event listeners for search filters
    document.querySelectorAll("thead input").forEach(input => {
        input.addEventListener("keyup", filterTable);
    });
    
    // Initial update
    updateAnalyticsTable();
    
    // Update every 2 seconds
    updateInterval = setInterval(updateAnalyticsTable, 2000);
    
    console.log('Real-time crowd analytics system active');
});

// Cleanup when page unloads
window.addEventListener('beforeunload', function() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
});
</script>
{% endblock content %}

{% block javascripts %}
{% endblock javascripts %}