
import cv2, time, threading, json, os
from datetime import datetime, date
import numpy as np
import torch
from ultralytics import YOL<PERSON>
from huggingface_hub import login, hf_hub_download
from collections import defaultdict, deque
import configparser
import sys

# Try to read config.ini, fallback to environment variables
config = configparser.ConfigParser()
config_file = os.path.join(os.path.dirname(__file__), 'config.ini')

if os.path.exists(config_file):
    config.read(config_file)
    try:
        bytetrack_path = config.get('paths', 'bytetrack_path')
        huggingface_token = config.get('huggingface', 'token')
    except (configparser.NoSectionError, configparser.NoOptionError):
        # Fallback to environment variables
        bytetrack_path = os.getenv('BYTETRACK_PATH', '')
        huggingface_token = os.getenv('HUGGINGFACE_TOKEN', '')
else:
    # Use environment variables
    bytetrack_path = os.getenv('BYTETRACK_PATH', '')
    huggingface_token = os.getenv('HUGGINGFACE_TOKEN', '')

# Only add ByteTrack path if it exists and is not empty
if bytetrack_path and os.path.exists(bytetrack_path):
    sys.path.append(bytetrack_path)

# Try to import ByteTracker, use fallback if not available
try:
    from yolox.tracker.byte_tracker import BYTETracker
    BYTETRACK_AVAILABLE = True
except ImportError:
    print("⚠️  ByteTracker not available, tracking features disabled")
    BYTETracker = None
    BYTETRACK_AVAILABLE = False

def authenticate_huggingface():
    if huggingface_token:
        login(token=huggingface_token)
        print("Successfully authenticated with Hugging Face")
    else:
        print("⚠️  No HuggingFace token available")

# Call the authentication function at startup
authenticate_huggingface()
# ------------------------------
# Global Data & Lock
# ------------------------------
latest_frames = {}       # For MJPEG streaming of processed frames
tracking_data = {}       # Per-track data: trajectory, speed, direction, anomaly, dwell_time, etc.
camera_stats = {
    "cam1": {"entries": 0, "exits": 0, "people_count": 0},
    "cam2": {"entries": 0, "exits": 0, "people_count": 0},
    "cam3": {"entries": 0, "exits": 0, "people_count": 0}
}
people_count_buffer = {
    "cam1": deque(maxlen=30),  # Store last 30 frame counts (1 second at 30fps)
    "cam2": deque(maxlen=30), 
    "cam3": deque(maxlen=30)
}
stable_count_history = {
    "cam1": [],
    "cam2": [], 
    "cam3": []
}

last_count_update = {
    "cam1": 0,
    "cam2": 0,
    "cam3": 0
}
daily_student_count = 0
daily_entries_count = 0
last_reset_date = str(datetime.now().date())
student_count_history = []
max_concurrent_students = 0
previous_day_count = 0
# For daily tracking
detection_data = {}      # For weapon detection events
data_lock = threading.Lock()
crossed_line = {}        # To track which track IDs have crossed the virtual line
last_detection_time = {}
# ------------------------------
# Camera Sources & Model Loading
# ------------------------------
camera_sources = {
    "cam1": "rtsp://virtualpresenz.duckdns.org:8554/stream0",
    "cam2": "rtsp://virtualpresenz2.duckdns.org:8553/stream0",
    "cam3": "rtsp://virtualpresenz1.duckdns.org:8555/stream0"
}

output_dir = "apps/static/detection"  # Directory to save detections
os.makedirs(output_dir, exist_ok=True)
def download_model_from_hf(repo_id, filename):
    """Downloads a model file from Hugging Face and returns the local path"""
    try:
        # Replace with your actual repository and model filenames
        local_path = hf_hub_download(
            repo_id=repo_id,  
            filename=filename
        )
        print(f"Downloaded {filename} from {repo_id} to {local_path}")
        return local_path
    except Exception as e:
        print(f"Error downloading model {filename} from {repo_id}: {e}")
        raise

# Download and load models
try:
    # Replace with your actual repository ID and filenames
    repo_id = "VirtualPresenz/crowd_analytics"
    
    crowd_model_path = download_model_from_hf("VirtualPresenz/crowd_analytics", "best_yolo11s_crowd.pt")
    weapon_model_path = download_model_from_hf("VirtualPresenz/Gundetection", "best_yolo11x_gun.pt")
    
     # Load the original PT models
    print("Loading original PT models...")
    crowd_model = YOLO(crowd_model_path)
    weapon_model = YOLO(weapon_model_path)
    
   # If you need TensorRT acceleration, uncomment these lines
    
    # TensorRT export disabled - using production inference system
    print("⚠️  TensorRT export disabled in legacy inference.py")
    print("   Using production inference system for optimized models")

    # Skip TensorRT conversion
    # import subprocess
    # subprocess.run([
    #     "yolo", "export",
    #     f"model={crowd_model_path}",
    #     "format=engine",
    #     "device=0"
    # ])
    # subprocess.run([
    #     "yolo", "export",
    #     f"model={weapon_model_path}",
    #     "format=engine",
    #     "device=0"
    # ])
    
    # TensorRT loading disabled - using PyTorch models in legacy system
    print("Using PyTorch models in legacy inference system")
    # export_path_crowd = crowd_model_path.replace(".pt", ".engine")
    # export_path_weapon = weapon_model_path.replace(".pt", ".engine")

    # Skip TensorRT loading - use PyTorch models directly
    # if os.path.exists(export_path_crowd) and os.path.exists(export_path_weapon):
    #     print("Loading TensorRT models...")
    #     crowd_model = YOLO(export_path_crowd)
    #     weapon_model = YOLO(export_path_weapon)
    #     print("TensorRT models loaded successfully")
    # else:
    #     print("TensorRT export failed, using PyTorch models")
    
except Exception as e:
    print(f"Error loading models: {e}")
# # Make sure the paths below point to your engine files
# crowd_model = YOLO("C:/Users/<USER>/Downloads/pt_files/best_yolo11s_crowd.engine")
# weapon_model = YOLO("C:/Users/<USER>/Downloads/pt_files/best_yolo11x_gun.engine")

# ------------------------------
# Tracker Setup
# ------------------------------
class EnhancedByteTrackArgs:
    """Enhanced ByteTracker configuration for more stable tracking"""
    def __init__(self, 
                 track_thresh=0.6,      # Higher threshold for more stable tracks
                 match_thresh=0.7,      # Higher matching threshold
                 track_buffer=120,      # Longer buffer to maintain IDs through gaps
                 mot20=False,
                 frame_rate=30):
        self.track_thresh = track_thresh     # Confidence threshold for track creation
        self.match_thresh = match_thresh     # Threshold for track matching
        self.track_buffer = track_buffer     # Frames to keep lost tracks
        self.mot20 = mot20
        self.min_box_area = 100             # Minimum detection area (filters noise)
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.frame_rate = frame_rate

trackers = {
        "cam1": BYTETracker(EnhancedByteTrackArgs(
            track_thresh=0.7,      # Higher confidence threshold
            match_thresh=0.8,      # Stricter matching
            track_buffer=150,      # 5 seconds at 30fps
            frame_rate=30
        )),
        "cam2": BYTETracker(EnhancedByteTrackArgs(
            track_thresh=0.7,
            match_thresh=0.8,
            track_buffer=150,
            frame_rate=30
        )),
        "cam3": BYTETracker(EnhancedByteTrackArgs(
            track_thresh=0.7,
            match_thresh=0.8,
            track_buffer=150,
            frame_rate=30
        ))
    }
def get_stable_people_count():
    """
    Get stable people count using multiple approaches to handle ID changes
    """
    current_time = time.time()
    stable_counts = {}
    
    with data_lock:
        for cam_id in ["cam1", "cam2", "cam3"]:
            # Method 1: Use the raw people_count from camera_stats (most reliable)
            raw_count = camera_stats[cam_id]["people_count"]
            
            # Method 2: Smooth the count using recent history
            people_count_buffer[cam_id].append(raw_count)
            
            if len(people_count_buffer[cam_id]) > 0:
                # Use median of recent counts to smooth out fluctuations
                recent_counts = list(people_count_buffer[cam_id])
                smoothed_count = int(np.median(recent_counts))
                
                # Use max of recent counts if there's significant variation
                # (helps catch brief detection gaps)
                max_recent = max(recent_counts[-10:]) if len(recent_counts) >= 10 else raw_count
                
                # Choose the more conservative estimate
                final_count = max(smoothed_count, raw_count) if abs(max_recent - raw_count) <= 2 else raw_count
            else:
                final_count = raw_count
            
            # Store in history for trend analysis
            stable_count_history[cam_id].append({
                "timestamp": current_time,
                "count": final_count,
                "raw_count": raw_count,
                "smoothed": smoothed_count if 'smoothed_count' in locals() else raw_count
            })
            
            # Keep only last 100 entries per camera
            if len(stable_count_history[cam_id]) > 100:
                stable_count_history[cam_id].pop(0)
            
            stable_counts[cam_id] = final_count
            last_count_update[cam_id] = current_time
    
    return stable_counts

def get_tracking_based_count_with_stability():
    """
    Alternative approach: Use tracking data but with stability improvements
    """
    current_time = time.time()
    tracking_data_local = get_tracking_data()
    
    # Group tracks by camera and apply stability logic
    camera_tracks = defaultdict(list)
    
    for track_id, track_data in tracking_data_local.items():
        camera = track_data.get('camera', '')
        if camera in ['cam1', 'cam2', 'cam3']:
            # Calculate last activity time
            start_time = track_data.get('start_time', 0)
            dwell_time = track_data.get('dwell_time', 0)
            last_activity = start_time + dwell_time
            
            # Consider track active if updated within last 5 seconds
            # (shorter timeout due to ID instability)
            if current_time - last_activity <= 5:
                camera_tracks[camera].append({
                    'track_id': track_id,
                    'last_activity': last_activity,
                    'dwell_time': dwell_time,
                    'trajectory_length': len(track_data.get('trajectory', [])),
                    'position': track_data.get('trajectory', [[0,0,0,0]])[-1] if track_data.get('trajectory') else None
                })
    
    # Apply spatial deduplication to handle ID changes for same person
    stable_camera_counts = {}
    
    for camera, tracks in camera_tracks.items():
        if not tracks:
            stable_camera_counts[camera] = 0
            continue
        
        # Sort tracks by last activity (most recent first)
        tracks.sort(key=lambda x: x['last_activity'], reverse=True)
        
        # Remove spatially close tracks (likely same person with different IDs)
        deduplicated_tracks = []
        
        for track in tracks:
            if track['position'] is None:
                continue
                
            x1, y1, x2, y2 = track['position']
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            
            # Check if this position is too close to existing tracks
            is_duplicate = False
            for existing_track in deduplicated_tracks:
                if existing_track['position'] is None:
                    continue
                    
                ex1, ey1, ex2, ey2 = existing_track['position']
                existing_center_x, existing_center_y = (ex1 + ex2) // 2, (ey1 + ey2) // 2
                
                # Calculate distance between centers
                distance = np.sqrt((center_x - existing_center_x)**2 + (center_y - existing_center_y)**2)
                
                # If distance is less than 100 pixels, consider it the same person
                if distance < 100:
                    # Keep the track with longer trajectory (more stable)
                    if track['trajectory_length'] > existing_track['trajectory_length']:
                        # Replace the existing track
                        deduplicated_tracks.remove(existing_track)
                        deduplicated_tracks.append(track)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                deduplicated_tracks.append(track)
        
        stable_camera_counts[camera] = len(deduplicated_tracks)
    
    return stable_camera_counts

def get_hybrid_people_count():
    """
    Combines detection-based counting with tracking analytics
    Returns most reliable count possible
    """
    # Method 1: Stable detection-based count
    stable_counts = get_stable_people_count()
    
    # Method 2: Tracking-based count with deduplication
    tracking_counts = get_tracking_based_count_with_stability()
    
    # Combine approaches
    final_counts = {}
    current_time = time.time()
    
    for cam_id in ["cam1", "cam2", "cam3"]:
        detection_count = stable_counts.get(cam_id, 0)
        tracking_count = tracking_counts.get(cam_id, 0)
        
        # If counts are close, use detection count (more reliable)
        if abs(detection_count - tracking_count) <= 2:
            final_count = detection_count
            method = "detection_stable"
        # If tracking count is much higher, detection might have missed people
        elif tracking_count > detection_count + 2:
            final_count = min(detection_count + 2, tracking_count)  # Conservative increase
            method = "tracking_assisted"
        # If detection count is much higher, tracking might have duplicates
        else:
            final_count = detection_count
            method = "detection_preferred"
        
        final_counts[cam_id] = {
            "count": final_count,
            "detection_count": detection_count,
            "tracking_count": tracking_count,
            "method": method,
            "timestamp": current_time
        }
    
    return final_counts
def cleanup_old_tracking_data():
    """Remove very old tracking data to prevent memory buildup"""
    current_time = time.time()
    cutoff_time = current_time - 3600  # Remove tracks older than 1 hour
    
    with data_lock:
        tracks_to_remove = []
        for track_id, track_data in tracking_data.items():
            last_activity = track_data.get('start_time', 0) + track_data.get('dwell_time', 0)
            if last_activity < cutoff_time:
                tracks_to_remove.append(track_id)
        
        for track_id in tracks_to_remove:
            del tracking_data[track_id]
            # Also remove from crossed_line if exists
            if track_id in crossed_line:
                del crossed_line[track_id]
    
    if tracks_to_remove:
        print(f"Cleaned up {len(tracks_to_remove)} old tracking entries")

# Enhanced detection preprocessing for better tracking
def preprocess_detections_for_stable_tracking(detections, frame_shape):
    """
    Preprocess detections to improve tracking stability
    """
    if len(detections) == 0:
        return np.empty((0, 5))
    
    processed_detections = []
    
    for detection in detections:
        x1, y1, x2, y2, conf = detection
        
        # Filter by size - remove very small detections
        width = x2 - x1
        height = y2 - y1
        area = width * height
        
        if area < 500:  # Minimum area threshold
            continue
        
        # Filter by aspect ratio - person should be taller than wide
        aspect_ratio = height / width if width > 0 else 0
        if aspect_ratio < 1.2 or aspect_ratio > 4.0:  # Reasonable person proportions
            continue
        
        # Filter by position - remove detections at image edges (often false positives)
        margin = 20
        if (x1 < margin or y1 < margin or 
            x2 > frame_shape[1] - margin or y2 > frame_shape[0] - margin):
            continue
        
        processed_detections.append([x1, y1, x2, y2, conf])
    
    return np.array(processed_detections) if processed_detections else np.empty((0, 5))
def process_frame_crowd_with_stable_tracking(frame, cam_id, tracker):
    """
    Enhanced crowd processing with stable tracking and fallback counting
    """
    results = crowd_model(frame)
    raw_detections = []
    detection_count = 0
    
    # Process YOLO detections
    for result in results:
        for box in result.boxes:
            x1, y1, x2, y2 = map(int, box.xyxy[0])
            conf = float(box.conf[0])
            if conf >= 0.8:  # High confidence threshold
                raw_detections.append([x1, y1, x2, y2, conf])
                detection_count += 1
                
                # Draw detection rectangle
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 255), 2)
                cv2.putText(frame, f"Person {conf:.2f}", (x1, y1 - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
    
    # Preprocess detections for stable tracking
    processed_detections = preprocess_detections_for_stable_tracking(
        raw_detections, frame.shape
    )
    
    # Update tracker
    if len(processed_detections) > 0:
        tracks = tracker.update(processed_detections, 
                              (frame.shape[0], frame.shape[1]), 
                              (frame.shape[0], frame.shape[1]))
    else:
        tracks = []
    
    print(f"Camera {cam_id}: {detection_count} detections, {len(tracks)} tracks")
    
    # Display counts on frame
    cv2.putText(frame, f"Detected: {detection_count}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
    cv2.putText(frame, f"Tracked: {len(tracks)}", (10, 60), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
    
    with data_lock:
        # Primary count: Use detection count (most reliable)
        camera_stats[cam_id]["people_count"] = detection_count
        
        # Update tracking data (for analytics, not primary counting)
        for track in tracks:
            x1, y1, x2, y2 = map(int, track.tlbr)
            track_id = track.track_id
            
            # Initialize or update track data
            if track_id not in tracking_data:
                tracking_data[track_id] = {
                    "camera": cam_id,
                    "trajectory": [],
                    "start_time": time.time(),
                    "speed_history": [],
                    "current_speed": 0,
                    "movement_direction": "Stationary",
                    "anomaly": "Normal",
                    "dwell_time": 0,
                    "detection_count": detection_count,
                    "track_stability": "new"
                }
            else:
                # Mark as stable if track has persisted
                if len(tracking_data[track_id]["trajectory"]) > 10:
                    tracking_data[track_id]["track_stability"] = "stable"
            
            # Update trajectory and timing
            tracking_data[track_id]["trajectory"].append([x1, y1, x2, y2])
            tracking_data[track_id]["dwell_time"] = time.time() - tracking_data[track_id]["start_time"]
            tracking_data[track_id]["detection_count"] = detection_count
            
            # Calculate movement
            speed, direction = calculate_movement(tracking_data[track_id]["trajectory"])
            tracking_data[track_id]["speed_history"].append(speed)
            tracking_data[track_id]["current_speed"] = speed
            tracking_data[track_id]["movement_direction"] = direction
            tracking_data[track_id]["anomaly"] = detect_anomaly(tracking_data[track_id]["speed_history"])
            
            # Line crossing detection for entry/exit counting
            line_y = 200 if cam_id == "cam1" else 300
            check_line_crossing(track_id, tracking_data[track_id]["trajectory"], line_y, cam_id)
            
            # Draw tracking visualization
            color = (0, 255, 0) if tracking_data[track_id]["track_stability"] == "stable" else (0, 0, 255)
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            cv2.putText(frame, f"ID {track_id} ({direction})", (x1, y2 + 15),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
    
    return frame

# Periodic cleanup function
def periodic_cleanup():
    """Run periodic cleanup tasks"""
    while True:
        try:
            time.sleep(300)  # Run every 5 minutes
            cleanup_old_tracking_data()
        except Exception as e:
            print(f"Error in periodic cleanup: {e}")

# Start cleanup thread
def start_cleanup_thread():
    """Start the cleanup thread"""
    cleanup_thread = threading.Thread(target=periodic_cleanup, daemon=True)
    cleanup_thread.start()
    print("Started periodic cleanup thread")
# class ByteTrackArgs:
#     def __init__(self, track_thresh=0.25, match_thresh=0.5, track_buffer=80, mot20=False):
#         self.track_thresh = track_thresh
#         self.match_thresh = match_thresh
#         self.track_buffer = track_buffer
#         self.mot20 = mot20
#         self.min_box_area = 5
#         self.device = "cuda" if torch.cuda.is_available() else "cpu"

# trackers = {
#     "cam1": BYTETracker(ByteTrackArgs()),
#     "cam2": BYTETracker(ByteTrackArgs()),
#     "cam3": BYTETracker(ByteTrackArgs())
# }

# ------------------------------
# Helper Functions (Crowd Analytics)
# ------------------------------
def reset_daily_counts_if_new_day():
    """Reset daily counters if it's a new day"""
    global daily_student_count, daily_entries_count, last_reset_date
    global previous_day_count, student_count_history, max_concurrent_students
    
    current_date = str(date.today())
    if current_date != last_reset_date:
        # Store previous day's data for comparison
        previous_day_count = daily_student_count
        
        # Save historical data
        save_historical_data()
        
        # Reset counters
        daily_entries_count = sum(camera_stats[cam]["entries"] for cam in camera_stats)
        last_reset_date = current_date
        max_concurrent_students = 0
        
        # Reset camera daily stats
        with data_lock:
            for cam in camera_stats:
                camera_stats[cam]["daily_max"] = 0
                camera_stats[cam]["hourly_counts"] = []
        
        print(f"[{datetime.now()}] Daily counts reset for new day: {current_date}")

def save_historical_data():
    """Save historical data for trend analysis"""
    try:
        historical_file = "apps/static/historical_data.json"
        
        # Load existing data
        historical_data = {}
        if os.path.exists(historical_file):
            with open(historical_file, 'r') as f:
                historical_data = json.load(f)
        
        # Add today's data
        today = str(date.today())
        historical_data[today] = {
            "total_entries": daily_entries_count,
            "max_concurrent": max_concurrent_students,
            "hourly_counts": student_count_history.copy(),
            "camera_stats": camera_stats.copy()
        }
        
        # Keep only last 30 days
        dates = list(historical_data.keys())
        if len(dates) > 30:
            for old_date in sorted(dates)[:-30]:
                del historical_data[old_date]
        
        # Save updated data
        with open(historical_file, 'w') as f:
            json.dump(historical_data, f, indent=4)
            
    except Exception as e:
        print(f"Error saving historical data: {e}")

def update_hourly_count():
    """Update hourly student count for trend analysis"""
    global student_count_history, max_concurrent_students
    
    current_hour = datetime.now().hour
    total_current = sum(camera_stats[cam]["people_count"] for cam in camera_stats)
    
    # Update max concurrent students
    if total_current > max_concurrent_students:
        max_concurrent_students = total_current
    
    # Store hourly count (keep last 24 hours)
    if len(student_count_history) >= 24:
        student_count_history.pop(0)
    
    student_count_history.append({
        "hour": current_hour,
        "count": total_current,
        "timestamp": time.time()
    })

def calculate_percentage_change():
    """Calculate percentage change compared to previous periods"""
    current_total = sum(camera_stats[cam]["people_count"] for cam in camera_stats)
    current_entries = sum(camera_stats[cam]["entries"] for cam in camera_stats)
    
    # Simple percentage calculation based on entries vs previous day
    if previous_day_count > 0:
        change = ((current_entries - previous_day_count) / previous_day_count) * 100
        if change > 0:
            return f"+{change:.0f}%"
        else:
            return f"{change:.0f}%"
    else:
        return "+15%"
def check_line_crossing(track_id, trajectory, line_y, cam_id):
    if len(trajectory) < 2:
        return
    prev_x1, prev_y1, prev_x2, prev_y2 = trajectory[-2]
    curr_x1, curr_y1, curr_x2, curr_y2 = trajectory[-1]
    prev_center_y = (prev_y1 + prev_y2) // 2
    curr_center_y = (curr_y1 + curr_y2) // 2

    # Detect entry
    if prev_center_y < line_y and curr_center_y >= line_y:
        if track_id not in crossed_line or crossed_line[track_id] != "entered":
            crossed_line[track_id] = "entered"
            camera_stats[cam_id]["entries"] += 1
    # Detect exit
    elif prev_center_y > line_y and curr_center_y <= line_y:
        if track_id not in crossed_line or crossed_line[track_id] != "exited":
            crossed_line[track_id] = "exited"
            camera_stats[cam_id]["exits"] += 1

def calculate_movement(trajectory):
    if len(trajectory) < 2:
        return 0, "Stationary"
    prev = trajectory[-2]
    curr = trajectory[-1]
    prev_center = ((prev[0] + prev[2]) // 2, (prev[1] + prev[3]) // 2)
    curr_center = ((curr[0] + curr[2]) // 2, (curr[1] + curr[3]) // 2)
    speed = np.linalg.norm(np.array(curr_center) - np.array(prev_center))
    dx = curr_center[0] - prev_center[0]
    dy = curr_center[1] - prev_center[1]
    if abs(dx) > abs(dy):
        direction = "Right" if dx > 0 else "Left"
    else:
        direction = "Down" if dy > 0 else "Up"
    return speed, direction

def detect_anomaly(speed_history):
    if len(speed_history) < 5:
        return "Normal"
    avg_speed = np.mean(speed_history[-5:])
    current_speed = speed_history[-1]
    if current_speed > avg_speed * 2:
        return "Anomaly: Sudden Speed Change"
    if current_speed < avg_speed * 0.5 and current_speed > 0:
        return "Anomaly: Sudden Stop"
    return "Normal"

# ------------------------------
# Inference Functions
# ------------------------------
def process_frame_crowd(frame, cam_id, tracker):
    """Run crowd analytics on a frame and update tracking_data and camera_stats."""
    results = crowd_model(frame)
    detections = []
    people_num = 0
    for result in results:
        for box in result.boxes:
            x1, y1, x2, y2 = map(int, box.xyxy[0])
            conf = float(box.conf[0])
            if conf >= 0.8:
                detections.append([x1, y1, x2, y2, conf])
                people_num += 1
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 255), 2)
                cv2.putText(frame, f"Person {conf:.2f}", (x1, y1 - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
    cv2.putText(frame, f"People: {people_num}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    dets = np.array(detections) if detections else np.empty((0, 5))
    tracks = tracker.update(dets, (frame.shape[0], frame.shape[1]), (frame.shape[0], frame.shape[1]))
    print(f"Detected {len(tracks)} and detections as {len(detections)} people {people_num} in {cam_id}")
    with data_lock:
        camera_stats[cam_id]["people_count"] = people_num
        for track in tracks:
            x1, y1, x2, y2 = map(int, track.tlbr)
            track_id = track.track_id
            if track_id not in tracking_data:
                tracking_data[track_id] = {
                    "camera": cam_id,
                    "trajectory": [],
                    "start_time": time.time(),
                    "speed_history": [],
                    "current_speed": 0,
                    "movement_direction": "Stationary",
                    "anomaly": "Normal",
                    "dwell_time": 0,
                    "person_count": people_num
                }
            tracking_data[track_id]["trajectory"].append([x1, y1, x2, y2])
            tracking_data[track_id]["dwell_time"] = time.time() - tracking_data[track_id]["start_time"]
            speed, direction = calculate_movement(tracking_data[track_id]["trajectory"])
            tracking_data[track_id]["speed_history"].append(speed)
            tracking_data[track_id]["current_speed"] = speed
            tracking_data[track_id]["movement_direction"] = direction
            tracking_data[track_id]["anomaly"] = detect_anomaly(tracking_data[track_id]["speed_history"])
            # Define virtual line (use 200 for cam1, 300 for others)
            line_y = 200 if cam_id == "cam1" else 300
            check_line_crossing(track_id, tracking_data[track_id]["trajectory"], line_y, cam_id)
            # Draw bounding box with speed and direction info
            cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 0, 0), 2)
            cv2.putText(frame, f"ID {track_id} ({direction}, {speed:.2f}px/frame)", (x1, y2 + 15),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
    return frame

# def process_frame_weapon(frame, cam_id):
#     """Run weapon detection on the frame and update detection_data."""
#     results = weapon_model(frame)
#     detected = False
#     for result in results:
#         for box in result.boxes:
#             x1, y1, x2, y2 = map(int, box.xyxy[0])
#             conf = float(box.conf[0])
#             class_id = int(box.cls[0])
#             if class_id == 0 and conf > 0.60:
#                 detected = True
#                 cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
#                 cv2.putText(frame, f"Handgun {conf:.2f}", (x1, y1 - 10),
#                             cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
#     if detected:
#         timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
#         image_filename = os.path.join(output_dir, f"{cam_id}_detection_{timestamp}.jpg")
#         cv2.imwrite(image_filename, frame)
#         with data_lock:
#             if cam_id not in detection_data:
#                 detection_data[cam_id] = []
#             detection_data[cam_id].append({
#                 "timestamp": timestamp,
#                 "camera": cam_id,
#                 "image_path": image_filename
#             })
#     return frame

def process_frame_weapon(frame, cam_id):
    results = weapon_model(frame)
    detected = False
    for result in results:
        for box in result.boxes:
            x1, y1, x2, y2 = map(int, box.xyxy[0])
            conf = float(box.conf[0])
            class_id = int(box.cls[0])
            if class_id == 0 and conf > 0.80:
                detected = True
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
                cv2.putText(frame, f"Handgun {conf:.2f}", (x1, y1 - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    if detected:
        current_epoch = time.time()
        with data_lock:
            last_logged = last_detection_time.get(cam_id, 0)
            # Log a new detection event only if 1 second has passed
            if current_epoch - last_logged < 0.5:
                return frame
            last_detection_time[cam_id] = current_epoch
        
        # Use a format that doesn't include colons which can cause issues in filenames
        timestamp = datetime.now().strftime("%Y-%m-%d_%H--%M--%S")
        
        # Make sure these directories exist
        detection_dir = "apps/static/assets/detection"
        os.makedirs(detection_dir, exist_ok=True)
        
        # Create both file path and URL path
        image_filename = os.path.join(detection_dir, f"{cam_id}_detection_{timestamp}.jpg")
        # URL path for web access (adjust this based on your Flask static folder configuration)
        image_url = f"/static/assets/detection/{cam_id}_detection_{timestamp}.jpg"
        
        # Save the image
        success = cv2.imwrite(image_filename, frame)
        print(f"Saving image to: {os.path.abspath(image_filename)}, Success: {success}")
        
        # Check if file was saved successfully
        if os.path.exists(image_filename):
            print(f"File exists at: {image_filename}")
        else:
            print(f"WARNING: File does not exist at: {image_filename}")
            
        with data_lock:
            if cam_id not in detection_data:
                detection_data[cam_id] = []
            detection_data[cam_id].append({
                "timestamp": timestamp,
                "image_path": image_url
            })
    return frame


def process_camera(cam_id, video_source):
    """Continuously process a camera feed and run both crowd and weapon detections."""
    cap = cv2.VideoCapture(video_source, cv2.CAP_FFMPEG)
    if not cap.isOpened():
        print(f"[ERROR] Could not open camera {cam_id}")
        return
    tracker = trackers[cam_id]
    while True:
        ret, frame = cap.read()
        if not ret:
            print(f"[ERROR] Camera {cam_id} feed lost.")
            break
        frame = process_frame_crowd(frame, cam_id, tracker)
        frame = process_frame_weapon(frame, cam_id)
        with data_lock:
            latest_frames[cam_id] = frame.copy()
        time.sleep(0.03)
    cap.release()

# def process_camera():
#     """Process cameras one after another in a sequence rather than in parallel."""
#     while True:
#         for cam_id, source in camera_sources.items():
#             try:
#                 # Process just a single frame from each camera in sequence
#                 cap = cv2.VideoCapture(source)
#                 if not cap.isOpened():
#                     print(f"[ERROR] Could not open camera {cam_id}")
#                     continue
                    
#                 ret, frame = cap.read()
#                 cap.release()
                
#                 if not ret:
#                     print(f"[WARNING] Failed to read frame from {cam_id}")
#                     continue
                
#                 tracker = trackers[cam_id]
                
#                 # Process the frame
#                 processed = process_frame_crowd(frame.copy(), cam_id, tracker)
#                 processed = process_frame_weapon(processed, cam_id)
                
#                 # Store the processed frame
#                 with data_lock:
#                     latest_frames[cam_id] = processed
                
#             except Exception as e:
#                 print(f"[ERROR] Exception processing {cam_id}: {e}")
            
#         # Small delay between cycles
#         time.sleep(0.1)

# ------------------------------
# Utility & JSON Logging
# ------------------------------
def start_camera_threads():
    """Start a background thread for each camera source."""
    for cam_id, source in camera_sources.items():
        thread = threading.Thread(target=process_camera, args=(cam_id, source), daemon=True)
        thread.start()

def generate_video_stream(cam_id):
    """Generator for MJPEG stream of the latest processed frame."""
    while True:
        with data_lock:
            frame = latest_frames.get(cam_id)
        if frame is not None:
            ret, buffer = cv2.imencode('.jpg', frame)
            if ret:
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')
        else:
            time.sleep(0.1)

def get_tracking_data():
    with data_lock:
        return tracking_data.copy()

def get_detection_data():
    with data_lock:
        return detection_data.copy()

def get_camera_stats():
    with data_lock:
        return camera_stats.copy()

def save_json_logs(interval=10):
    """Periodically save tracking_data and camera_stats to disk."""
    while True:
        time.sleep(interval)
        with data_lock:
            data_to_save = {
                "tracking": tracking_data,
                "camera_stats": camera_stats
            }
            with open("tracking_data.json", "w") as f:
                json.dump(data_to_save, f, indent=4)
            with open(os.path.join(output_dir, "detection_data.json"), "w") as f:
                json.dump(detection_data, f, indent=4)
        print(f"[{datetime.now()}] JSON logs saved.")
