{% extends "layouts/base.html" %}

{% block title %} Notifications {% endblock %} 

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}

 <div class="container-fluid py-4">
    <div class="row">
      <div class="col-12">
        <div class="card my-4">
          <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
            <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3">
              <h6 class="text-white text-capitalize ps-3">Weapon Detection & Analytics
            </div>
          </div>
          <div class="card-body px-0 pb-2">
            <div class="table-responsive p-0">
              <table class="table align-items-center mb-0" id="weaponDetectionTable">
                <thead>

                  <tr>
                      <th><input type="text" class="form-control" placeholder="Search Timestamp" onkeyup="filterTable(0)"></th>
                      <th><input type="text" class="form-control" placeholder="Search Weapon" onkeyup="filterTable(1)"></th>
                      <th><input type="text" class="form-control" placeholder="Search Image" onkeyup="filterTable(2)"></th>
                      <th><input type="text" class="form-control" placeholder="Search Video" onkeyup="filterTable(3)"></th>
                      <th><input type="text" class="form-control" placeholder="Search Camera Location" onkeyup="filterTable(4)"></th>
                      <th><input type="text" class="form-control" placeholder="Search Camera Index" onkeyup="filterTable(5)"></th>
                      <th><input type="text" class="form-control" placeholder="Search Admin log" onkeyup="filterTable(6)"></th>
                      <th><input type="text" class="form-control" placeholder="Search Nearest Entry" onkeyup="filterTable(7)"></th>
                      <th><input type="text" class="form-control" placeholder="Search Nearest Exit" onkeyup="filterTable(8)"></th>
                      <th><input type="text" class="form-control" placeholder="Search Resolved" onkeyup="filterTable(9)"></th>
                       <th><input type="text" class="form-control" placeholder="Search Action" onkeyup="filterTable(10)"></th>
                  </tr>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Alert Timestamp</th>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Detected Weapon</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Image</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Video</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Camera Location</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Camera Index</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Admin log</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Nearest Entry</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Nearest Exit</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Resolved</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- <tr>
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm" id ="currentdate" >23/04/18, 10:24:10</h6>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p class="text-xs font-weight-bold mb-0">HandGun</p>
                    </td>
                    <td class>
                      <span class="text-xs font-weight-bold mb-0">Image</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">Video</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 2nd Floor</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 4,5</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">log details </span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">entry details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">exit details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Yes</span>
                    </td>  <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Closed</span>
                    </td>

<tr>
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm" id ="currentdate" >23/04/18, 10:24:10</h6>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p class="text-xs font-weight-bold mb-0">HandGun</p>
                    </td>
                    <td class>
                      <span class="text-xs font-weight-bold mb-0">Image</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">Video</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 1st Floor</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 4,5</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">log details </span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">entry details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">exit details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Yes</span>
                    </td>  <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Closed</span>
                    </td>
<tr>
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm" id ="currentdate" >23/04/18, 10:24:10</h6>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p class="text-xs font-weight-bold mb-0">HandGun</p>
                    </td>
                    <td class>
                      <span class="text-xs font-weight-bold mb-0">Image</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">Video</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 2nd Floor</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 4,5</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">log details </span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">entry details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">exit details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Yes</span>
                    </td>  <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Closed</span>
                    </td>
<tr>
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm" id ="currentdate" >23/04/18, 10:24:10</h6>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p class="text-xs font-weight-bold mb-0">HandGun</p>
                    </td>
                    <td class>
                      <span class="text-xs font-weight-bold mb-0">Image</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">Video</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 4th Floor</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 4,5</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">log details </span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">entry details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">exit details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Yes</span>
                    </td>  <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Closed</span>
                    </td>

                    </td>
                  </tr>
                   <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm" id ="currentdate" >23/04/18, 10:24:10</h6>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p class="text-xs font-weight-bold mb-0">HandGun</p>
                    </td>
                    <td class>
                      <span class="text-xs font-weight-bold mb-0">Image</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">Video</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 1st Floor</span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> 4,5</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">log details </span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">entry details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">exit details</span>
                    </td>
                      <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Yes</span>
                    </td>  <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold"> Closed</span>
                    </td> -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

<script>
function filterTable() {
    let table, tr, i, j, td, txtValue;
    table = document.getElementById("analyticsTable");
    tr = table.getElementsByTagName("tr");

    // Get all search inputs
    let inputs = document.querySelectorAll("thead input");

    // Loop through all table rows (excluding header rows)
    for (i = 2; i < tr.length; i++) {
        let match = true; // Assume row is visible unless a column filter fails

        // Check all columns
        for (j = 0; j < inputs.length; j++) {
            let filter = inputs[j].value.toLowerCase();
            td = tr[i].getElementsByTagName("td")[j];

            if (td && filter) { // Only apply filter if input is not empty
                txtValue = td.textContent || td.innerText;
                if (!txtValue.toLowerCase().includes(filter)) {
                    match = false; // If one column doesn't match, hide row
                    break;
                }
            }
        }

        // Show or hide row based on filter match
        tr[i].style.display = match ? "" : "none";
    }
}

// document.addEventListener("DOMContentLoaded", function(){
//   fetch('/json/detection')
//     .then(response => response.json())
//     .then(data => {
//       // data is expected to be an object with keys like "cam1", "cam2", etc.
//       const tableBody = document.querySelector("#weaponDetectionTable tbody");
//       tableBody.innerHTML = "";  // clear any existing rows
//       // Map camera IDs to human-friendly labels:
//       const camMapping = {
//         "cam1": { location: "Floor 1 - Main Entrance", index: "1" },
//         "cam2": { location: "Floor 2 - Cafeteria", index: "2" },
//         "cam3": { location: "Floor 3 - Library", index: "3" }
//       };
//       // Iterate over each camera's detection events
//       for (const cam in data) {
//         data[cam].forEach(event => {
//           const camInfo = camMapping[cam] || { location: "Unknown", index: "N/A" };
//           const row = document.createElement("tr");
//           row.innerHTML = `
//             <td>${event.timestamp}</td>
//             <td>Handgun</td>
//             <td><a href="${event.image_path}" target="_blank">Image</a></td>
//             <td>N/A</td>
//             <td>${camInfo.location}</td>
//             <td>${camInfo.index}</td>
//             <td>log details</td>
//             <td>entry details</td>
//             <td>exit details</td>
//             <td>Yes</td>
//             <td>Closed</td>
//           `;
//           tableBody.appendChild(row);
//         });
//       }
//     })
//     .catch(error => {
//       console.error("Error fetching detection data:", error);
//     });
// });

document.addEventListener("DOMContentLoaded", function(){
  function updateDetectionTable() {
    fetch('/json/detection')
      .then(response => response.json())
      .then(data => {
        const tableBody = document.querySelector("#weaponDetectionTable tbody");
        tableBody.innerHTML = "";  // clear existing rows
        
        // Map camera IDs to human-friendly labels
        const camMapping = {
          "cam1": { location: "Floor 1 - Main Entrance", index: "1" },
          "cam2": { location: "Floor 2 - Cafeteria", index: "2" },
          "cam3": { location: "Floor 3 - Library", index: "3" }
        };
        
        // Collect all detection events in a single array
        const allEvents = [];
        for (const cam in data) {
          if (data[cam] && data[cam].length) {
            data[cam].forEach(event => {
              allEvents.push({
                ...event,
                camera: cam
              });
            });
          }
        }
        
        // Sort by timestamp descending (newest first)
        // Parse timestamp strings to sort them correctly
        allEvents.sort((a, b) => {
          const timeA = new Date(a.timestamp.replace(/_/g, ' ').replace(/--/g, ':').replace(/-/g, '/'));
          const timeB = new Date(b.timestamp.replace(/_/g, ' ').replace(/--/g, ':').replace(/-/g, '/'));
          return timeB - timeA;
        });
        
        // Create table rows for sorted events
        allEvents.forEach(event => {
          const cam = event.camera;
          const camInfo = camMapping[cam] || { location: "Unknown", index: "N/A" };
          const row = document.createElement("tr");
          
          // Format timestamp for better readability
          const formattedTime = event.timestamp.replace(/_/g, ' ').replace(/--/g, ':').replace(/-/g, '/');
          
          row.innerHTML = `
            <td>
              <div class="d-flex px-2 py-1">
                <div class="d-flex flex-column justify-content-center">
                  <h6 class="mb-0 text-sm">${formattedTime}</h6>
                </div>
              </div>
            </td>
            <td>
              <p class="text-xs font-weight-bold mb-0">Handgun</p>
            </td>
            <td class="align-middle text-center">
              <a href="${event.image_path}" target="_blank" class="text-xs font-weight-bold mb-0">View Image</a>
            </td>
            <td class="align-middle text-center">
              <span class="text-secondary text-xs font-weight-bold">N/A</span>
            </td>
            <td class="align-middle text-center">
              <span class="text-secondary text-xs font-weight-bold">${camInfo.location}</span>
            </td>
            <td class="align-middle text-center">
              <span class="text-secondary text-xs font-weight-bold">${camInfo.index}</span>
            </td>
            <td class="align-middle text-center">
              <span class="text-secondary text-xs font-weight-bold">Detection logged</span>
            </td>
            <td class="align-middle text-center">
              <span class="text-secondary text-xs font-weight-bold">--</span>
            </td>
            <td class="align-middle text-center">
              <span class="text-secondary text-xs font-weight-bold">--</span>
            </td>
            <td class="align-middle text-center">
              <span class="text-secondary text-xs font-weight-bold">No</span>
            </td>
            <td class="align-middle text-center">
              <button class="btn btn-sm btn-danger">Respond</button>
            </td>
          `;
          tableBody.appendChild(row);
        });
      })
      .catch(error => {
        console.error("Error fetching detection data:", error);
      });
  }
  
  // Initial update
  updateDetectionTable();
  
  // Update every 5 seconds (to reduce performance impact)
  setInterval(updateDetectionTable, 2000);
});

// Attach event listeners to all input fields
document.addEventListener("DOMContentLoaded", function () {
    let inputs = document.querySelectorAll("thead input");
    inputs.forEach(input => {
        input.addEventListener("keyup", filterTable);
    });
});
</script>
{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}{% endblock javascripts %}
