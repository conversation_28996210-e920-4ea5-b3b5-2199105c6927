"""
Production Configuration for Flask Application
Optimized for hosting and deployment
"""

import os
from datetime import timedelta

class ProductionConfig:
    """Production configuration with security and performance optimizations"""
    
    # Basic Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-change-in-production'
    
    # Database configuration (if using database)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///production.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 0
    }
    
    # Redis configuration
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # Session configuration
    SESSION_TYPE = 'redis'
    SESSION_PERMANENT = False
    SESSION_USE_SIGNER = True
    SESSION_KEY_PREFIX = 'inference:'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # Security settings
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    
    # File upload settings
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB max file size
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or './uploads'
    
    # Logging configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or './logs/app.log'
    
    # Performance settings
    SEND_FILE_MAX_AGE_DEFAULT = timedelta(hours=1)
    
    # Model and inference settings
    MODEL_CACHE_DIR = os.environ.get('MODEL_CACHE_DIR') or './models_cache'
    USE_CLOUD_MODELS = os.environ.get('USE_CLOUD_MODELS', 'true').lower() == 'true'
    
    # Camera settings
    CAMERA_URLS = {
        'cam1': os.environ.get('CAM1_URL', 'rtsp://default-cam1-url'),
        'cam2': os.environ.get('CAM2_URL', 'rtsp://default-cam2-url'),
        'cam3': os.environ.get('CAM3_URL', 'rtsp://default-cam3-url')
    }
    
    # Detection settings
    CROWD_CONFIDENCE = float(os.environ.get('CROWD_CONFIDENCE', '0.85'))
    WEAPON_CONFIDENCE = float(os.environ.get('WEAPON_CONFIDENCE', '0.85'))
    
    # Performance limits
    MAX_FPS = int(os.environ.get('MAX_FPS', '15'))
    WORKER_THREADS = int(os.environ.get('WORKER_THREADS', '3'))
    
    # Monitoring and health checks
    HEALTH_CHECK_INTERVAL = int(os.environ.get('HEALTH_CHECK_INTERVAL', '30'))
    METRICS_RETENTION_HOURS = int(os.environ.get('METRICS_RETENTION_HOURS', '24'))
    
    # Static file serving
    STATIC_FOLDER = 'static'
    STATIC_URL_PATH = '/static'
    
    # Template settings
    TEMPLATES_AUTO_RELOAD = False
    
    @staticmethod
    def init_app(app):
        """Initialize application with production settings"""
        
        # Configure logging
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            # File handler
            if not os.path.exists('logs'):
                os.mkdir('logs')
            
            file_handler = RotatingFileHandler(
                'logs/inference.log',
                maxBytes=10240000,
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('Inference application startup')

class DevelopmentConfig:
    """Development configuration"""
    
    DEBUG = True
    SECRET_KEY = 'dev-secret-key'
    SQLALCHEMY_DATABASE_URI = 'sqlite:///dev.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Development-specific settings
    TEMPLATES_AUTO_RELOAD = True
    USE_CLOUD_MODELS = False
    
    # Relaxed security for development
    WTF_CSRF_ENABLED = False
    
    @staticmethod
    def init_app(app):
        """Initialize application with development settings"""
        pass

class TestingConfig:
    """Testing configuration"""
    
    TESTING = True
    SECRET_KEY = 'test-secret-key'
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Disable CSRF for testing
    WTF_CSRF_ENABLED = False
    
    @staticmethod
    def init_app(app):
        """Initialize application with testing settings"""
        pass

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': ProductionConfig
}
