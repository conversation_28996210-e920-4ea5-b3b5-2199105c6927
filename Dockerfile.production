# Production Dockerfile for Inference System
# Optimized for cloud deployment with TensorRT support

# Use NVIDIA PyTorch base image with CUDA support
FROM nvcr.io/nvidia/pytorch:23.08-py3

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libglib2.0-0 \
    libgl1-mesa-glx \
    libgstreamer1.0-0 \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-plugins-ugly \
    gstreamer1.0-libav \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt requirements-production.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-production.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/models_cache \
             /app/static/detection \
             /app/static/assets/detection \
             /app/logs

# Set environment variables
ENV PYTHONPATH=/app
ENV FLASK_ENV=production
ENV USE_CLOUD_MODELS=true
ENV MODEL_CACHE_DIR=/app/models_cache
ENV OUTPUT_DIR=/app/static/detection
ENV DETECTION_DIR=/app/static/assets/detection
ENV LOGS_DIR=/app/logs

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# Create non-root user for security
RUN useradd -m -u 1000 appuser && \
    chown -R appuser:appuser /app
USER appuser

# Default command
CMD ["python", "run_production.py", "--mode", "flask"]

# Alternative commands for different deployment scenarios:
# Standalone mode: CMD ["python", "run_production.py", "--mode", "standalone"]
# Gunicorn: CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "--worker-class", "gevent", "apps:create_app()"]
