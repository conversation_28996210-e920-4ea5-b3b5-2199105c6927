version: '3.8'

services:
  # Main inference application
  inference-app:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: inference-app
    restart: unless-stopped
    
    # GPU support
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    
    # Environment variables
    environment:
      - USE_CLOUD_MODELS=true
      - FLASK_ENV=production
      - MODEL_CACHE_DIR=/app/models_cache
      - OUTPUT_DIR=/app/static/detection
      - DETECTION_DIR=/app/static/assets/detection
      - LOGS_DIR=/app/logs
      - MAX_FPS=15
      - WORKER_THREADS=3
      - GPU_MEMORY_FRACTION=0.7
      - CROWD_CONFIDENCE=0.85
      - WEAPON_CONFIDENCE=0.85
    
    # Environment file (create from .env.example)
    env_file:
      - .env
    
    # Volumes
    volumes:
      - ./models_cache:/app/models_cache
      - ./static:/app/static
      - ./logs:/app/logs
      - ./config:/app/config:ro
    
    # Ports
    ports:
      - "5000:5000"
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # Dependencies
    depends_on:
      - redis
    
    # Networks
    networks:
      - inference-network

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: inference-redis
    restart: unless-stopped
    
    # Configuration
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    
    # Volumes
    volumes:
      - redis-data:/data
    
    # Ports (internal only)
    expose:
      - "6379"
    
    # Health check
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    # Networks
    networks:
      - inference-network

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: inference-nginx
    restart: unless-stopped
    
    # Configuration
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./static:/app/static:ro
      - ./logs/nginx:/var/log/nginx
    
    # Ports
    ports:
      - "80:80"
      - "443:443"
    
    # Dependencies
    depends_on:
      - inference-app
    
    # Networks
    networks:
      - inference-network

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: inference-prometheus
    restart: unless-stopped
    
    # Configuration
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    # Ports
    ports:
      - "9090:9090"
    
    # Command
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    # Networks
    networks:
      - inference-network

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: inference-grafana
    restart: unless-stopped
    
    # Environment
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    
    # Volumes
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    # Ports
    ports:
      - "3000:3000"
    
    # Dependencies
    depends_on:
      - prometheus
    
    # Networks
    networks:
      - inference-network

# Networks
networks:
  inference-network:
    driver: bridge

# Volumes
volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
