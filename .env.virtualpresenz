# VirtualPresenz Cloud Models Configuration
# Copy this to .env and add your HuggingFace token

# Enable cloud model downloading
USE_CLOUD_MODELS=true

# Your HuggingFace token (get from https://huggingface.co/settings/tokens)
HUGGINGFACE_TOKEN=*************************************

# Your HuggingFace repositories
HF_CROWD_REPO=VirtualPresenz/crowd_analytics
HF_WEAPON_REPO=VirtualPresenz/Gundetection

# Model filenames in your repos
CROWD_MODEL_FILENAME=best_yolo11s_crowd.engine
WEAPON_MODEL_FILENAME=best_yolo11x_gun.engine

# Cache settings
MODEL_CACHE_DIR=./models_cache
CACHE_TTL_HOURS=24
MAX_CACHE_SIZE_GB=10.0

# Camera URLs
CAM1_URL=rtsp://virtualpresenz.duckdns.org:8554/stream0
CAM2_URL=rtsp://virtualpresenz2.duckdns.org:8553/stream0
CAM3_URL=rtsp://virtualpresenz1.duckdns.org:8555/stream0

# Performance settings
MAX_FPS=15
WORKER_THREADS=3
GPU_MEMORY_FRACTION=0.7

# Detection thresholds
CROWD_CONFIDENCE=0.85
WEAPON_CONFIDENCE=0.85

# Output directories
OUTPUT_DIR=apps/static/detection
DETECTION_DIR=apps/static/assets/detection
LOGS_DIR=./logs

# Flask settings
FLASK_ENV=production
SECRET_KEY=your-production-secret-key-change-me
LOG_LEVEL=INFO

# Fallback local models (if cloud download fails)
CROWD_MODEL_PATH=./models/best_yolo11s_crowd.engine
WEAPON_MODEL_PATH=./models/best_yolo11x_gun.engine
