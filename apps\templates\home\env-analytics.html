{% extends "layouts/base.html" %}

{% block title %} Speech and Action Analytics  {% endblock %}

{% block stylesheets %}{% endblock stylesheets %}

{% block content %}

 <div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card my-4">
                <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                    <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3">
                        <h6 class="text-white text-capitalize ps-3">Speech and Action Analytics</h6>
                    </div>
                </div>
                <div class="card-body px-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0" id="analyticsTable">
                            <thead>
                                <tr>
                                    <th><input type="text" class="form-control" placeholder="Search Timestamp" onkeyup="filterTable(0)"></th>
                                    <th><input type="text" class="form-control" placeholder="Search Input" onkeyup="filterTable(1)"></th>
                                    <th><input type="text" class="form-control" placeholder="Search Output" onkeyup="filterTable(2)"></th>
                                    <th><input type="text" class="form-control" placeholder="Search Classification" onkeyup="filterTable(3)"></th>
                                    <th><input type="text" class="form-control" placeholder="Search Streak of Instances" onkeyup="filterTable(4)"></th>
                                    <th><input type="text" class="form-control" placeholder="Search Confidence Level" onkeyup="filterTable(4)"></th>
                                    <th><input type="text" class="form-control" placeholder="Search Device Info" onkeyup="filterTable(5)"></th>
                                </tr>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Timestamp</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Input</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Output</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Classification</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Streak of Instances</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Confidence Level</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Device Info</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>23/04/18, 10:24:10</td>
                                    <td>Voice</td>
                                    <td>"How are you"</td>
                                    <td class="text-center">Normal</td>
                                    <td class="text-center">n/a</td>
                                    <td class="text-center">High</td>
                                    <td class="text-center">Microphone 2</td>
                                </tr>
                                <tr>
                                    <td>23/04/18, 10:24:10</td>
                                    <td>Action</td>
                                    <td>Video</td>
                                    <td class="text-center">Aggression</td>
                                    <td class="text-center">n/a</td>
                                    <td class="text-center">Low</td>
                                    <td class="text-center">Camera 1</td>
                                </tr>
                                <tr>
                                    <td>22/04/18, 12:21:09</td>
                                    <td>Voice</td>
                                    <td>"Put your hands up"</td>
                                    <td class="text-center">Aggression</td>
                                    <td class="text-center">3</td>
                                    <td class="text-center">High</td>
                                    <td class="text-center">Microphone 1</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterTable() {
    let table, tr, i, j, td, txtValue;
    table = document.getElementById("analyticsTable");
    tr = table.getElementsByTagName("tr");

    // Get all search inputs
    let inputs = document.querySelectorAll("thead input");

    // Loop through all table rows (excluding header rows)
    for (i = 2; i < tr.length; i++) {
        let match = true; // Assume row is visible unless a column filter fails

        // Check all columns
        for (j = 0; j < inputs.length; j++) {
            let filter = inputs[j].value.toLowerCase();
            td = tr[i].getElementsByTagName("td")[j];

            if (td && filter) { // Only apply filter if input is not empty
                txtValue = td.textContent || td.innerText;
                if (!txtValue.toLowerCase().includes(filter)) {
                    match = false; // If one column doesn't match, hide row
                    break;
                }
            }
        }

        // Show or hide row based on filter match
        tr[i].style.display = match ? "" : "none";
    }
}

// Attach event listeners to all input fields
document.addEventListener("DOMContentLoaded", function () {
    let inputs = document.querySelectorAll("thead input");
    inputs.forEach(input => {
        input.addEventListener("keyup", filterTable);
    });
});
</script>



{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}{% endblock javascripts %}