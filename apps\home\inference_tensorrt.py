"""
TensorRT Optimized Inference Module
High-performance inference using TensorRT engines
"""

import cv2
import time
import threading
import os
import logging
import queue
import numpy as np
from typing import Dict, Optional, Tuple, Any
from datetime import datetime
from ultralytics import YOLO
import torch

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TensorRTInferenceEngine:
    """TensorRT optimized inference engine"""
    
    def __init__(self):
        self.crowd_model = None
        self.weapon_model = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # Model paths from environment
        self.crowd_engine_path = os.getenv('CROWD_TENSORRT_MODEL', './models/best_yolo11s_crowd.engine')
        self.weapon_engine_path = os.getenv('WEAPON_TENSORRT_MODEL', './models/best_yolo11x_gun.engine')
        
        # Fallback PyTorch models
        self.crowd_pt_path = os.getenv('CROWD_PT_MODEL', './models/best_yolo11s_crowd.pt')
        self.weapon_pt_path = os.getenv('WEAPON_PT_MODEL', './models/best_yolo11x_gun.pt')
        
        # Detection thresholds
        self.crowd_confidence = float(os.getenv('CROWD_CONFIDENCE', '0.8'))
        self.weapon_confidence = float(os.getenv('WEAPON_CONFIDENCE', '0.8'))
        
        # Performance settings
        self.max_fps = int(os.getenv('MAX_FPS', '30'))
        
        # Output directories
        self.output_dir = os.getenv('OUTPUT_DIR', 'apps/static/detection')
        self.detection_dir = os.getenv('DETECTION_DIR', 'apps/static/assets/detection')
        
        # Create output directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.detection_dir, exist_ok=True)
        
        # Camera data storage
        self.camera_stats = {
            "cam1": {"people_count": 0, "entries": 0, "exits": 0},
            "cam2": {"people_count": 0, "entries": 0, "exits": 0},
            "cam3": {"people_count": 0, "entries": 0, "exits": 0}
        }
        self.detection_data = {"cam1": [], "cam2": [], "cam3": []}
        self.latest_frames = {}
        
        # Thread safety
        self.lock = threading.Lock()
        
        logger.info("TensorRT Inference Engine initialized")
    
    def load_models(self) -> bool:
        """Load TensorRT engines with fallback to PyTorch"""
        try:
            logger.info("Loading TensorRT models...")
            
            # Load crowd model
            if os.path.exists(self.crowd_engine_path):
                logger.info(f"Loading crowd TensorRT engine: {self.crowd_engine_path}")
                self.crowd_model = YOLO(self.crowd_engine_path)
                logger.info("✅ Crowd TensorRT engine loaded")
            elif os.path.exists(self.crowd_pt_path):
                logger.warning(f"TensorRT engine not found, using PyTorch model: {self.crowd_pt_path}")
                self.crowd_model = YOLO(self.crowd_pt_path)
                logger.info("✅ Crowd PyTorch model loaded")
            else:
                logger.error("No crowd model found (TensorRT or PyTorch)")
                return False
            
            # Load weapon model
            if os.path.exists(self.weapon_engine_path):
                logger.info(f"Loading weapon TensorRT engine: {self.weapon_engine_path}")
                self.weapon_model = YOLO(self.weapon_engine_path)
                logger.info("✅ Weapon TensorRT engine loaded")
            elif os.path.exists(self.weapon_pt_path):
                logger.warning(f"TensorRT engine not found, using PyTorch model: {self.weapon_pt_path}")
                self.weapon_model = YOLO(self.weapon_pt_path)
                logger.info("✅ Weapon PyTorch model loaded")
            else:
                logger.error("No weapon model found (TensorRT or PyTorch)")
                return False
            
            # Warm up models
            self._warmup_models()
            
            logger.info("🚀 All models loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load models: {e}")
            return False
    
    def _warmup_models(self):
        """Warm up models with dummy inference"""
        try:
            logger.info("Warming up models...")
            dummy_frame = np.zeros((640, 640, 3), dtype=np.uint8)
            
            if self.crowd_model:
                self.crowd_model(dummy_frame, verbose=False)
                logger.info("Crowd model warmed up")
            
            if self.weapon_model:
                self.weapon_model(dummy_frame, verbose=False)
                logger.info("Weapon model warmed up")
                
        except Exception as e:
            logger.warning(f"Model warmup failed: {e}")
    
    def process_crowd_detection(self, frame: np.ndarray) -> Tuple[int, np.ndarray]:
        """Process crowd detection"""
        if self.crowd_model is None:
            return 0, frame
        
        try:
            start_time = time.time()
            results = self.crowd_model(frame, verbose=False)
            inference_time = time.time() - start_time
            
            people_count = 0
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        conf = float(box.conf[0])
                        if conf >= self.crowd_confidence:
                            people_count += 1
                            x1, y1, x2, y2 = map(int, box.xyxy[0])
                            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 255), 2)
                            cv2.putText(frame, f"Person {conf:.2f}", (x1, y1 - 10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
            
            logger.debug(f"Crowd detection: {people_count} people, {inference_time*1000:.1f}ms")
            return people_count, frame
            
        except Exception as e:
            logger.error(f"Crowd detection error: {e}")
            return 0, frame
    
    def process_weapon_detection(self, frame: np.ndarray, cam_id: str) -> Tuple[bool, np.ndarray]:
        """Process weapon detection"""
        if self.weapon_model is None:
            return False, frame
        
        try:
            start_time = time.time()
            results = self.weapon_model(frame, verbose=False)
            inference_time = time.time() - start_time
            
            detected = False
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        conf = float(box.conf[0])
                        class_id = int(box.cls[0])
                        
                        if class_id == 0 and conf >= self.weapon_confidence:
                            detected = True
                            x1, y1, x2, y2 = map(int, box.xyxy[0])
                            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 3)
                            cv2.putText(frame, f"WEAPON {conf:.2f}", (x1, y1 - 10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
            if detected:
                logger.warning(f"🚨 WEAPON DETECTED in {cam_id}")
            
            logger.debug(f"Weapon detection: {detected}, {inference_time*1000:.1f}ms")
            return detected, frame
            
        except Exception as e:
            logger.error(f"Weapon detection error: {e}")
            return False, frame
    
    def process_frame(self, frame: np.ndarray, cam_id: str) -> np.ndarray:
        """Process a single frame with both detections"""
        try:
            # Crowd detection
            people_count, frame = self.process_crowd_detection(frame)
            
            # Update camera stats
            with self.lock:
                self.camera_stats[cam_id]["people_count"] = people_count
            
            # Weapon detection
            weapon_detected, frame = self.process_weapon_detection(frame, cam_id)
            
            if weapon_detected:
                # Save detection image
                self.save_detection_image(frame, cam_id)
            
            # Add overlay information
            cv2.putText(frame, f"Camera: {cam_id}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"People: {people_count}", (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), (10, frame.shape[0] - 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Store latest frame
            with self.lock:
                self.latest_frames[cam_id] = frame.copy()
            
            return frame
            
        except Exception as e:
            logger.error(f"Frame processing error for {cam_id}: {e}")
            return frame
    
    def save_detection_image(self, frame: np.ndarray, cam_id: str):
        """Save weapon detection image"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{cam_id}_weapon_{timestamp}.jpg"
            filepath = os.path.join(self.detection_dir, filename)
            
            success = cv2.imwrite(filepath, frame)
            
            if success:
                logger.info(f"Saved detection image: {filename}")
                
                # Add to detection data
                with self.lock:
                    detection_record = {
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "camera": cam_id,
                        "image_path": f"/static/assets/detection/{filename}",
                        "confidence": "high"
                    }
                    self.detection_data[cam_id].append(detection_record)
                    
                    # Keep only last 50 detections per camera
                    if len(self.detection_data[cam_id]) > 50:
                        self.detection_data[cam_id] = self.detection_data[cam_id][-25:]
            else:
                logger.error(f"Failed to save detection image: {filename}")
                
        except Exception as e:
            logger.error(f"Error saving detection image: {e}")
    
    def get_camera_stats(self) -> Dict[str, Any]:
        """Get camera statistics"""
        with self.lock:
            return self.camera_stats.copy()
    
    def get_detection_data(self) -> Dict[str, Any]:
        """Get detection data"""
        with self.lock:
            return self.detection_data.copy()
    
    def get_latest_frame(self, cam_id: str) -> Optional[np.ndarray]:
        """Get latest processed frame"""
        with self.lock:
            return self.latest_frames.get(cam_id)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status"""
        return {
            "models_loaded": self.crowd_model is not None and self.weapon_model is not None,
            "crowd_model_type": "TensorRT" if self.crowd_engine_path in str(self.crowd_model.model) else "PyTorch",
            "weapon_model_type": "TensorRT" if self.weapon_engine_path in str(self.weapon_model.model) else "PyTorch",
            "device": self.device,
            "cuda_available": torch.cuda.is_available(),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "N/A"
        }

# Global inference engine instance
_inference_engine = None

def get_inference_engine() -> TensorRTInferenceEngine:
    """Get or create the global inference engine"""
    global _inference_engine
    if _inference_engine is None:
        _inference_engine = TensorRTInferenceEngine()
    return _inference_engine

# Flask integration functions
def start_camera_threads() -> bool:
    """Initialize the inference engine"""
    engine = get_inference_engine()
    return engine.load_models()

def process_frame(frame: np.ndarray, cam_id: str) -> np.ndarray:
    """Process frame through inference engine"""
    engine = get_inference_engine()
    return engine.process_frame(frame, cam_id)

def get_camera_stats() -> Dict[str, Any]:
    """Get camera statistics"""
    engine = get_inference_engine()
    return engine.get_camera_stats()

def get_detection_data() -> Dict[str, Any]:
    """Get detection data"""
    engine = get_inference_engine()
    return engine.get_detection_data()

def get_latest_frame(cam_id: str) -> Optional[np.ndarray]:
    """Get latest frame"""
    engine = get_inference_engine()
    return engine.get_latest_frame(cam_id)

def get_system_status() -> Dict[str, Any]:
    """Get system status"""
    engine = get_inference_engine()
    return engine.get_system_status()

if __name__ == "__main__":
    # Test the inference engine
    engine = TensorRTInferenceEngine()
    
    if engine.load_models():
        print("✅ Models loaded successfully")
        
        # Test with dummy frame
        dummy_frame = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        processed_frame = engine.process_frame(dummy_frame, "test")
        
        print("✅ Frame processing test successful")
        print(f"📊 System status: {engine.get_system_status()}")
    else:
        print("❌ Failed to load models")
