<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="/static/assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="/static/assets/img/favicon.png">
  <title>
    Virtual Presenz - {% block title %}{% endblock %} | Sky Stike
  </title>
  <!--     Fonts and icons     -->
  <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900|Roboto+Slab:400,700" />
  <!-- Nucleo Icons -->
  <link href="/static/assets/css/nucleo-icons.css" rel="stylesheet" />
  <link href="/static/assets/css/nucleo-svg.css" rel="stylesheet" />
  <!-- Font Awesome Icons -->
  <script defer src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.0/js/all.min.js" crossorigin="anonymous"></script>
  <!-- Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
  <!-- CSS Files -->
  <link id="pagestyle" href="/static/assets/css/material-dashboard.css?v=3.0.0" rel="stylesheet" />

  {% block stylesheets %}{% endblock stylesheets %}

</head>
<body class="bg-gray-200">
  
  {% include 'includes/navigation-fullscreen.html' %}

  <main class="main-content mt-0">
    
    {% block content %}{% endblock content %}

  </main>

  {% include 'includes/scripts.html' %}

  <!-- Specific Page JS goes HERE  -->
  {% block javascripts %}{% endblock javascripts %}

  <script>
    var win = navigator.platform.indexOf('Win') > -1;
    if (win && document.querySelector('#sidenav-scrollbar')) {
      var options = {
        damping: '0.5'
      }
      Scrollbar.init(document.querySelector('#sidenav-scrollbar'), options);
    }
  </script>
  <!-- Github buttons -->
  <script async defer src="https://buttons.github.io/buttons.js"></script>
  <!-- Control Center for Material Dashboard: parallax effects, scripts for the example pages etc -->
  <script src="/static/assets/js/material-dashboard.min.js?v=3.0.0"></script>

</body> 
</html>
