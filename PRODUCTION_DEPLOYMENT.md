# Production Deployment Guide

This guide covers deploying the Integrated Weapon and Crowd Detection System to production environments.

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- NVIDIA GPU with CUDA support
- NVIDIA Container Toolkit
- At least 8GB RAM and 4GB GPU memory

### 1. Environment Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd Integrated-weapon-and-crowd

# Copy environment configuration
cp production.env.example .env

# Edit .env with your settings
nano .env
```

### 2. Configure Environment Variables

Edit `.env` file with your specific settings:

```bash
# Cloud Models (Recommended)
USE_CLOUD_MODELS=true
HUGGINGFACE_TOKEN=hf_your_token_here
HF_MODEL_REPO=your-username/inference-models

# Camera URLs
CAM1_URL=rtsp://your-camera-1-url
CAM2_URL=rtsp://your-camera-2-url
CAM3_URL=rtsp://your-camera-3-url

# Security
SECRET_KEY=your-super-secret-production-key
```

### 3. Deploy with Dock<PERSON> Compose

```bash
# Build and start services
docker-compose -f docker-compose.production.yml up -d

# Check status
docker-compose -f docker-compose.production.yml ps

# View logs
docker-compose -f docker-compose.production.yml logs -f inference-app
```

### 4. Access Your Application

- **Main Application**: http://your-server-ip
- **Health Check**: http://your-server-ip/api/health
- **System Status**: http://your-server-ip/api/system-status

## 🏗️ Deployment Options

### Option 1: Docker Compose (Recommended)

Complete stack with nginx, redis, and monitoring:

```bash
docker-compose -f docker-compose.production.yml up -d
```

### Option 2: Standalone Docker

Single container deployment:

```bash
# Build image
docker build -f Dockerfile.production -t inference-app .

# Run container
docker run -d \
  --name inference-app \
  --gpus all \
  -p 5000:5000 \
  --env-file .env \
  -v $(pwd)/models_cache:/app/models_cache \
  -v $(pwd)/static:/app/static \
  inference-app
```

### Option 3: Direct Python

For development or custom deployments:

```bash
# Install dependencies
pip install -r requirements.txt
pip install -r requirements-production.txt

# Run application
python run_production.py --mode flask
```

## ☁️ Cloud Deployment

### AWS ECS

1. **Push to ECR**:
```bash
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account>.dkr.ecr.us-east-1.amazonaws.com
docker build -f Dockerfile.production -t inference-app .
docker tag inference-app:latest <account>.dkr.ecr.us-east-1.amazonaws.com/inference-app:latest
docker push <account>.dkr.ecr.us-east-1.amazonaws.com/inference-app:latest
```

2. **Create ECS Task Definition** with GPU support
3. **Deploy to ECS Service**

### Google Cloud Run

```bash
# Build and push
gcloud builds submit --tag gcr.io/PROJECT-ID/inference-app

# Deploy
gcloud run deploy inference-app \
  --image gcr.io/PROJECT-ID/inference-app \
  --platform managed \
  --region us-central1 \
  --set-env-vars USE_CLOUD_MODELS=true
```

### Azure Container Instances

```bash
# Create resource group
az group create --name inference-rg --location eastus

# Deploy container
az container create \
  --resource-group inference-rg \
  --name inference-app \
  --image your-registry/inference-app \
  --gpu-count 1 \
  --gpu-sku V100 \
  --environment-variables USE_CLOUD_MODELS=true
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `USE_CLOUD_MODELS` | Enable cloud model downloading | `true` |
| `HUGGINGFACE_TOKEN` | HuggingFace API token | Required |
| `HF_MODEL_REPO` | Model repository | Required |
| `CAM1_URL`, `CAM2_URL`, `CAM3_URL` | Camera RTSP URLs | Required |
| `MAX_FPS` | Maximum processing FPS | `15` |
| `WORKER_THREADS` | Processing threads | `3` |
| `GPU_MEMORY_FRACTION` | GPU memory usage | `0.7` |
| `CROWD_CONFIDENCE` | Crowd detection threshold | `0.85` |
| `WEAPON_CONFIDENCE` | Weapon detection threshold | `0.85` |

### Model Configuration

#### Cloud Models (Recommended)
```bash
USE_CLOUD_MODELS=true
HUGGINGFACE_TOKEN=hf_your_token
HF_MODEL_REPO=your-username/models
```

#### Local Models
```bash
USE_CLOUD_MODELS=false
CROWD_MODEL_PATH=./models/crowd_model.engine
WEAPON_MODEL_PATH=./models/weapon_model.engine
```

## 📊 Monitoring

### Health Checks

- **Application Health**: `/api/health`
- **System Status**: `/api/system-status`
- **Camera Status**: `/api/camera-stats`

### Logging

Logs are available in:
- Container: `/app/logs/`
- Host: `./logs/` (if volume mounted)

### Metrics

Access Prometheus metrics at:
- **Prometheus**: http://your-server:9090
- **Grafana**: http://your-server:3000 (admin/admin)

## 🔒 Security

### Production Security Checklist

- [ ] Change default `SECRET_KEY`
- [ ] Use HTTPS in production
- [ ] Secure camera stream URLs
- [ ] Limit network access to required ports
- [ ] Regular security updates
- [ ] Monitor access logs

### SSL/TLS Setup

1. **Obtain SSL Certificate**
2. **Update nginx configuration**:
```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    # ... rest of configuration
}
```

## 🚨 Troubleshooting

### Common Issues

#### Models Not Loading
```bash
# Check model download
docker logs inference-app | grep "model"

# Verify HuggingFace token
docker exec inference-app python -c "from huggingface_hub import login; login('your-token')"
```

#### Camera Connection Issues
```bash
# Test camera URLs
ffmpeg -i "rtsp://your-camera-url" -t 5 -f null -

# Check network connectivity
docker exec inference-app ping camera-ip
```

#### GPU Not Detected
```bash
# Verify GPU access
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi

# Check NVIDIA Container Toolkit
sudo systemctl status nvidia-container-toolkit
```

### Performance Optimization

#### GPU Memory
```bash
# Reduce GPU memory usage
GPU_MEMORY_FRACTION=0.5
```

#### Processing Performance
```bash
# Adjust processing settings
MAX_FPS=10
WORKER_THREADS=2
```

## 📈 Scaling

### Horizontal Scaling

Deploy multiple instances with load balancer:

```yaml
# docker-compose.scale.yml
version: '3.8'
services:
  inference-app:
    # ... configuration
    deploy:
      replicas: 3
  
  nginx:
    # ... load balancer configuration
```

### Vertical Scaling

Increase resources:
```bash
# More GPU memory
GPU_MEMORY_FRACTION=0.9

# More processing threads
WORKER_THREADS=6
```

## 🔄 Updates and Maintenance

### Application Updates

```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose -f docker-compose.production.yml build
docker-compose -f docker-compose.production.yml up -d
```

### Model Updates

Models are automatically updated when:
- Cache TTL expires (default: 24 hours)
- Model files are updated in cloud repository

### Backup and Recovery

```bash
# Backup configuration
tar -czf backup-$(date +%Y%m%d).tar.gz .env docker-compose.production.yml

# Backup detection data
tar -czf detections-$(date +%Y%m%d).tar.gz static/assets/detection/
```

## 📞 Support

For deployment issues:
1. Check logs: `docker-compose logs -f`
2. Verify configuration: `docker-compose config`
3. Test connectivity: Health check endpoints
4. Review this documentation
5. Contact support with logs and configuration details
