#!/usr/bin/env python3
"""
Test script for the production inference system
Tests models and API endpoints without cameras
"""

import os
import sys
import time
import requests
import numpy as np

def setup_test_environment():
    """Setup test environment"""
    # Set test mode
    os.environ['TEST_MODE'] = 'true'
    os.environ['USE_CLOUD_MODELS'] = 'true'
    
    # Add current directory to path
    if '.' not in sys.path:
        sys.path.insert(0, '.')

def test_model_loading():
    """Test model loading"""
    print("🧪 Testing model loading...")
    
    try:
        from apps.home.inference_production import ProductionConfig, ProductionModelManager, ProductionMetrics
        
        config = ProductionConfig()
        metrics = ProductionMetrics(config)
        model_manager = ProductionModelManager(config, metrics)
        
        if model_manager.load_models():
            print("✅ Models loaded successfully")
            return True
        else:
            print("❌ Failed to load models")
            return False
            
    except Exception as e:
        print(f"❌ Model loading error: {e}")
        return False

def test_inference():
    """Test inference with dummy data"""
    print("🧪 Testing inference...")
    
    try:
        from apps.home.inference_production import get_inference_system
        
        # Get inference system
        system = get_inference_system()
        
        # Create dummy frame
        dummy_frame = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # Test crowd detection
        people_count, processed_frame = system.model_manager.process_crowd_detection(dummy_frame)
        print(f"✅ Crowd detection: {people_count} people detected")
        
        # Test weapon detection
        weapon_detected, processed_frame = system.model_manager.process_weapon_detection(dummy_frame, "test")
        print(f"✅ Weapon detection: {'Weapon' if weapon_detected else 'No weapon'} detected")
        
        return True
        
    except Exception as e:
        print(f"❌ Inference error: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints"""
    print("🧪 Testing API endpoints...")
    
    base_url = "http://localhost:5000"
    
    endpoints = [
        "/api/health",
        "/api/system-status",
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint}: OK")
            else:
                print(f"❌ {endpoint}: Status {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint}: Connection error - {e}")

def main():
    """Main test function"""
    print("🚀 Production Inference System Test")
    print("=" * 50)
    
    # Setup test environment
    setup_test_environment()
    
    # Test model loading
    if not test_model_loading():
        print("❌ Model loading test failed")
        return False
    
    # Test inference
    if not test_inference():
        print("❌ Inference test failed")
        return False
    
    print("\n🎉 All tests passed!")
    print("\nTo test the full system:")
    print("1. Run: python run_production.py --mode flask")
    print("2. Open: http://localhost:5000")
    print("3. Test API endpoints")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
