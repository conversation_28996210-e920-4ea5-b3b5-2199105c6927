#!/bin/bash

# Exit on error, treat unset variables as errors, and ensure pipeline errors are caught.
set -euo pipefail

# --- Configuration ---
MAIN_REPO_NAME="Integrated-weapon-and-crowd"

# ByteTrack is public and will be cloned normally
BYTETRACK_REPO_URL="https://github.com/ifzhang/ByteTrack.git"
BYTETRACK_REPO_NAME="ByteTrack"

PYTHON_VERSION="python3" # Command to invoke python 3

# --- Helper Functions ---
log_info() {
    echo "INFO: $1"
}

log_error() {
    echo "ERROR: $1" >&2
}

# --- No SSH Key Setup Needed ---
# Files are transferred directly from GitHub Actions workflow

# --- Main Script ---
log_info "Starting project setup script..."

BASE_DIR=$(pwd)
VENV_NAME="env"
# Path variables will be set after determining repository location


# --- Step 2: Setup Main Repository ---
log_info "\n--- Step 2: Setup Main Repository ---"
# When running from GitHub Actions, files are already in the current directory
# Just use the current directory as the main repo path
if [ "$(basename "$BASE_DIR")" = "automated-setup" ]; then
    log_info "Using transferred files from GitHub Actions workflow..."
    log_info "Repository files ready at $BASE_DIR"
    MAIN_REPO_PATH="$BASE_DIR"
    VENV_PATH="$MAIN_REPO_PATH/$VENV_NAME"
    # Update Python and Pip executables with new path
    VENV_PYTHON_EXEC="$VENV_PATH/bin/$PYTHON_VERSION"
    VENV_PIP_EXEC="$VENV_PATH/bin/pip3"
    
    # On Windows (if using Git Bash or WSL), paths might differ slightly for venv executables
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$(uname -s | grep -i -E 'MINGW|MSYS|CYGWIN')" ]]; then
        VENV_PYTHON_EXEC="$VENV_PATH/Scripts/$PYTHON_VERSION.exe"
        VENV_PIP_EXEC="$VENV_PATH/Scripts/pip3.exe"
    fi
else
    log_error "Repository files not found. This script expects to run from transferred files."
    exit 1
fi

# --- Step 3: Install PyTorch with CUDA Support ---
log_info "\n--- Step 3: Install PyTorch with CUDA Support ---"
pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124


# --- Step 4: Set Up the Environment ---
log_info "\n--- Step 4: Set Up Virtual Environment ---"
cd "$MAIN_REPO_PATH" # Ensure we're in the repo directory for subsequent operations

if [ ! -d "$VENV_PATH" ]; then
    log_info "Creating Python virtual environment at '$VENV_PATH'..."
    "$PYTHON_VERSION" -m venv "$VENV_NAME"
else
    log_info "Virtual environment '$VENV_PATH' already exists. Skipping creation."
fi

log_info "Installing requirements for the main project using pip from venv..."
if [ -f "requirements.txt" ]; then
    "$VENV_PIP_EXEC" install -r requirements.txt
else
    log_error "Main requirements.txt not found in $MAIN_REPO_PATH. Skipping installation."
fi


# --- Step 5: Set Up ByteTrack ---
log_info "\n--- Step 5: Set Up ByteTrack ---"
BYTETRACK_PATH="$MAIN_REPO_PATH/$BYTETRACK_REPO_NAME"

if [ -d "$BYTETRACK_PATH" ]; then
    log_info "ByteTrack directory '$BYTETRACK_PATH' already exists. Skipping clone."
else
    log_info "Cloning ByteTrack from $BYTETRACK_REPO_URL..."
    # If ByteTrack were private and needed the SSH key:
    # setup_git_ssh_command # Ensure GIT_SSH_COMMAND is set if not already from main repo clone
    # git clone "$BYTETRACK_REPO_URL" "$BYTETRACK_PATH"
    # Assuming public for now:
    git clone "$BYTETRACK_REPO_URL" "$BYTETRACK_PATH"
fi

cd "$BYTETRACK_PATH" # Change to ByteTrack directory

BYTETRACK_REQ_FILE="requirements.txt"
if [ -f "$BYTETRACK_REQ_FILE" ]; then
    log_info "Editing ByteTrack requirements.txt to remove specified libraries..."
    # Libraries to remove (patterns for grep -E)
    # Escaping dots for literal match, using ^ for start of line.
    # onnx==1.8.1
    # onnxruntime==1.8.0
    # onnx-simplifier==0.3.5
    LIBRARIES_TO_REMOVE_PATTERN="^onnx==1\.8\.1|^onnxruntime==1\.8\.0|^onnx-simplifier==0\.3\.5"
    
    # Create a temporary file for the modified requirements
    TEMP_REQ_FILE="${BYTETRACK_REQ_FILE}.tmp"
    
    if grep -E "$LIBRARIES_TO_REMOVE_PATTERN" "$BYTETRACK_REQ_FILE" > /dev/null; then
        log_info "Found libraries to remove. Modifying $BYTETRACK_REQ_FILE..."
        grep -vE "$LIBRARIES_TO_REMOVE_PATTERN" "$BYTETRACK_REQ_FILE" > "$TEMP_REQ_FILE"
        mv "$TEMP_REQ_FILE" "$BYTETRACK_REQ_FILE"
        log_info "Successfully modified $BYTETRACK_REQ_FILE."
    else
        log_info "No specified libraries found to remove in $BYTETRACK_REQ_FILE (or they were already removed/commented)."
        # Clean up temp file if it was created but not used for mv
        if [ -f "$TEMP_REQ_FILE" ]; then
            rm "$TEMP_REQ_FILE"
        fi
    fi
else
    log_info "WARNING: ByteTrack requirements.txt not found. Skipping modification."
fi


log_info "Installing remaining requirements for ByteTrack using pip from venv..."
if [ -f "$BYTETRACK_REQ_FILE" ]; then
    "$VENV_PIP_EXEC" install -r "$BYTETRACK_REQ_FILE"
else
    log_info "WARNING: ByteTrack requirements.txt not found. Skipping pip install."
fi

log_info "Setting up ByteTrack package (develop mode)..."
if [ -f "setup.py" ]; then
    "$VENV_PYTHON_EXEC" setup.py develop
else
    log_info "WARNING: ByteTrack setup.py not found. Skipping setup."
fi

cd "$MAIN_REPO_PATH" # Change back to the main repository directory

log_info "\nSUCCESS: Automation script completed all steps successfully! 🎉"
