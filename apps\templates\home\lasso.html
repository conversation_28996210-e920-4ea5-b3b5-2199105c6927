{% extends "layouts/base.html" %}

{% block title %} Lasso {% endblock %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}
<style>
  .full-height-card {
    height: 90vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .camera-text {
    font-size: 100px; /* Increased font size */
    font-weight: bold;
    color: white;
    text-transform: uppercase; /* Ensures it remains capitalized */
  }
  .modal-content {
    width: 400px; /* Slightly larger popup width */
  }
  .modal-header {
    color: red;
    font-weight: bold;
    font-size: 1.2rem;
  }
</style>
{% endblock stylesheets %}

{% block content %}

  <div class="row mt-4 justify-content-center">
        <div class="col-lg-10 col-md-10 mt-4 mb-4"> <!-- Increased width -->
          <div class="card z-index-2 text-center full-height-card">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2 bg-transparent">
              <div class="bg-gradient-primary shadow-primary border-radius-lg py-3">
                <div class="camera-feed">
                  <div class="d-flex justify-content-center align-items-center" style="height: 420px;">
                    <h1 class="camera-text">CAMERA</h1>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-body">
              <h4 class="mb-3 text-center">CAMERA FEED</h4>
              <hr class="dark horizontal">

              <!-- Controls Section -->
              <div class="controls mt-2 text-center">
                <h5 class="mb-3">Controls</h5>
                <button class="btn btn-secondary mb-3 btn-lg" onclick="showPopup()">TRIGGER LASSO</button>
                <div class="d-flex justify-content-center gap-3">
                  <button class="btn btn-dark btn-md">▲</button>
                  <button class="btn btn-dark btn-md">◀</button>
                  <button class="btn btn-dark btn-md">▶</button>
                  <button class="btn btn-dark btn-md">▼</button>
                  <button class="btn btn-dark btn-md">+</button>
                  <button class="btn btn-dark btn-md">-</button>
                </div>
              </div>
            </div>
          </div>
        </div>
  </div>

  <!-- Popup Modal -->
  <div id="popup" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); justify-content: center; align-items: center;">
    <div class="modal-content bg-white p-4 rounded text-center">
      <h5 class="modal-header">WARNING !!</h5>
      <h5>Are you sure you want to trigger the Lasso?</h5>
      <div class="mt-3">
        <button class="btn btn-success me-2" onclick="triggerLasso()">YES - Trigger It</button>
        <button class="btn btn-danger" onclick="closePopup()">NO - Go Back to Screen</button>
      </div>
    </div>
  </div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script>
  function showPopup() {
    document.getElementById("popup").style.display = "flex";
  }

  function closePopup() {
    document.getElementById("popup").style.display = "none";
  }

  function triggerLasso() {
    closePopup();
    alert("Lasso Triggered!"); // Replace with actual trigger action
  }
</script>
{% endblock javascripts %}
