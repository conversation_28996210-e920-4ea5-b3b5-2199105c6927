"""
Cloud Model Manager for Production Inference
Supports downloading TensorRT models from various cloud sources
"""

import os
import hashlib
import json
import time
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass
import requests
from huggingface_hub import hf_hub_download, login

# Optional cloud imports
try:
    import boto3
    BOTO3_AVAILABLE = True
except ImportError:
    BOTO3_AVAILABLE = False

try:
    from google.cloud import storage as gcs
    GCS_AVAILABLE = True
except ImportError:
    GCS_AVAILABLE = False

@dataclass
class CloudModelConfig:
    """Configuration for cloud model sources"""
    
    # Hugging Face
    hf_token: Optional[str] = None
    hf_repo_id: str = "your-username/your-model-repo"
    
    # AWS S3
    aws_access_key: Optional[str] = None
    aws_secret_key: Optional[str] = None
    aws_bucket: str = "your-model-bucket"
    aws_region: str = "us-east-1"
    
    # Google Cloud Storage
    gcs_credentials_path: Optional[str] = None
    gcs_bucket: str = "your-model-bucket"
    
    # Direct URLs
    direct_urls: Dict[str, str] = None
    
    # Local cache settings
    cache_dir: str = "./models_cache"
    max_cache_size_gb: float = 10.0
    cache_ttl_hours: int = 24
    
    def __post_init__(self):
        if self.direct_urls is None:
            self.direct_urls = {}
        
        # Create cache directory
        os.makedirs(self.cache_dir, exist_ok=True)

class CloudModelManager:
    """Manages downloading and caching models from cloud sources"""
    
    def __init__(self, config: CloudModelConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.cache_metadata_file = os.path.join(config.cache_dir, "cache_metadata.json")
        self.cache_metadata = self._load_cache_metadata()
        
        # Initialize cloud clients
        self._init_cloud_clients()
    
    def _init_cloud_clients(self):
        """Initialize cloud service clients"""
        
        # Hugging Face
        if self.config.hf_token:
            try:
                login(token=self.config.hf_token)
                self.logger.info("✅ Hugging Face authenticated")
            except Exception as e:
                self.logger.warning(f"Hugging Face authentication failed: {e}")
        
        # AWS S3
        self.s3_client = None
        if BOTO3_AVAILABLE and self.config.aws_access_key and self.config.aws_secret_key:
            try:
                self.s3_client = boto3.client(
                    's3',
                    aws_access_key_id=self.config.aws_access_key,
                    aws_secret_access_key=self.config.aws_secret_key,
                    region_name=self.config.aws_region
                )
                self.logger.info("✅ AWS S3 client initialized")
            except Exception as e:
                self.logger.warning(f"AWS S3 initialization failed: {e}")
        
        # Google Cloud Storage
        self.gcs_client = None
        if GCS_AVAILABLE and self.config.gcs_credentials_path:
            try:
                self.gcs_client = gcs.Client.from_service_account_json(
                    self.config.gcs_credentials_path
                )
                self.logger.info("✅ Google Cloud Storage client initialized")
            except Exception as e:
                self.logger.warning(f"GCS initialization failed: {e}")
    
    def _load_cache_metadata(self) -> Dict[str, Any]:
        """Load cache metadata"""
        try:
            if os.path.exists(self.cache_metadata_file):
                with open(self.cache_metadata_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"Failed to load cache metadata: {e}")
        return {}
    
    def _save_cache_metadata(self):
        """Save cache metadata"""
        try:
            with open(self.cache_metadata_file, 'w') as f:
                json.dump(self.cache_metadata, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save cache metadata: {e}")
    
    def _get_file_hash(self, filepath: str) -> str:
        """Calculate file hash for integrity checking"""
        hash_md5 = hashlib.md5()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _is_cache_valid(self, model_name: str, local_path: str) -> bool:
        """Check if cached model is still valid"""
        if not os.path.exists(local_path):
            return False
        
        metadata = self.cache_metadata.get(model_name, {})
        
        # Check TTL
        cache_time = metadata.get('cached_at', 0)
        ttl_seconds = self.config.cache_ttl_hours * 3600
        if time.time() - cache_time > ttl_seconds:
            self.logger.info(f"Cache expired for {model_name}")
            return False
        
        # Check file integrity
        expected_hash = metadata.get('file_hash')
        if expected_hash:
            actual_hash = self._get_file_hash(local_path)
            if actual_hash != expected_hash:
                self.logger.warning(f"Cache corrupted for {model_name}")
                return False
        
        return True
    
    def download_from_huggingface(self, filename: str, local_path: str) -> bool:
        """Download model from Hugging Face"""
        try:
            self.logger.info(f"📤 Downloading {filename} from Hugging Face...")
            
            downloaded_path = hf_hub_download(
                repo_id=self.config.hf_repo_id,
                filename=filename,
                cache_dir=self.config.cache_dir,
                force_download=False
            )
            
            # Copy to expected location
            import shutil
            shutil.copy2(downloaded_path, local_path)
            
            self.logger.info(f"✅ Downloaded {filename} from Hugging Face")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to download {filename} from Hugging Face: {e}")
            return False
    
    def download_from_s3(self, s3_key: str, local_path: str) -> bool:
        """Download model from AWS S3"""
        if not self.s3_client:
            return False
        
        try:
            self.logger.info(f"Downloading {s3_key} from S3...")
            
            self.s3_client.download_file(
                self.config.aws_bucket,
                s3_key,
                local_path
            )
            
            self.logger.info(f"✅ Downloaded {s3_key} from S3")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to download {s3_key} from S3: {e}")
            return False
    
    def download_from_gcs(self, blob_name: str, local_path: str) -> bool:
        """Download model from Google Cloud Storage"""
        if not self.gcs_client:
            return False
        
        try:
            self.logger.info(f"Downloading {blob_name} from GCS...")
            
            bucket = self.gcs_client.bucket(self.config.gcs_bucket)
            blob = bucket.blob(blob_name)
            blob.download_to_filename(local_path)
            
            self.logger.info(f"✅ Downloaded {blob_name} from GCS")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to download {blob_name} from GCS: {e}")
            return False
    
    def download_from_url(self, url: str, local_path: str) -> bool:
        """Download model from direct URL"""
        try:
            self.logger.info(f"Downloading from URL: {url}")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            self.logger.info(f"✅ Downloaded from URL")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to download from URL {url}: {e}")
            return False
    
    def get_model(self, model_name: str, sources: Dict[str, str]) -> Optional[str]:
        """
        Download model from cloud sources with caching
        
        Args:
            model_name: Name of the model (e.g., 'crowd_model')
            sources: Dict of source_type -> source_path
                    e.g., {'huggingface': 'model.engine', 's3': 'models/model.engine'}
        
        Returns:
            Local path to the model file, or None if download failed
        """
        local_path = os.path.join(self.config.cache_dir, f"{model_name}.engine")
        
        # Check cache first
        if self._is_cache_valid(model_name, local_path):
            self.logger.info(f"✅ Using cached {model_name}")
            return local_path
        
        # Try downloading from sources in priority order
        download_methods = {
            'huggingface': self.download_from_huggingface,
            's3': self.download_from_s3,
            'gcs': self.download_from_gcs,
            'url': self.download_from_url
        }
        
        for source_type, source_path in sources.items():
            if source_type in download_methods:
                self.logger.info(f"Trying {source_type} for {model_name}")
                
                if download_methods[source_type](source_path, local_path):
                    # Update cache metadata
                    self.cache_metadata[model_name] = {
                        'cached_at': time.time(),
                        'source': source_type,
                        'source_path': source_path,
                        'file_hash': self._get_file_hash(local_path),
                        'file_size': os.path.getsize(local_path)
                    }
                    self._save_cache_metadata()
                    
                    return local_path
        
        self.logger.error(f"❌ Failed to download {model_name} from any source")
        return None
    
    def cleanup_cache(self):
        """Clean up old cache files"""
        try:
            cache_dir = Path(self.config.cache_dir)
            total_size = 0
            files_with_time = []
            
            # Calculate total cache size and collect file info
            for file_path in cache_dir.glob("*.engine"):
                if file_path.is_file():
                    size = file_path.stat().st_size
                    mtime = file_path.stat().st_mtime
                    total_size += size
                    files_with_time.append((file_path, mtime, size))
            
            # Convert to GB
            total_size_gb = total_size / (1024**3)
            
            if total_size_gb > self.config.max_cache_size_gb:
                self.logger.info(f"Cache size {total_size_gb:.2f}GB exceeds limit {self.config.max_cache_size_gb}GB")
                
                # Sort by modification time (oldest first)
                files_with_time.sort(key=lambda x: x[1])
                
                # Remove oldest files until under limit
                for file_path, _, size in files_with_time:
                    try:
                        file_path.unlink()
                        total_size -= size
                        total_size_gb = total_size / (1024**3)
                        
                        # Remove from metadata
                        model_name = file_path.stem
                        self.cache_metadata.pop(model_name, None)
                        
                        self.logger.info(f"Removed {file_path.name}")
                        
                        if total_size_gb <= self.config.max_cache_size_gb:
                            break
                    except Exception as e:
                        self.logger.error(f"Failed to remove {file_path}: {e}")
                
                self._save_cache_metadata()
            
        except Exception as e:
            self.logger.error(f"Cache cleanup failed: {e}")

# Integration with production inference
def create_cloud_model_manager() -> CloudModelManager:
    """Create cloud model manager from environment variables"""
    
    config = CloudModelConfig(
        # Hugging Face
        hf_token=os.getenv('HUGGINGFACE_TOKEN'),
        hf_repo_id=os.getenv('HF_MODEL_REPO', 'your-username/inference-models'),
        
        # AWS S3
        aws_access_key=os.getenv('AWS_ACCESS_KEY_ID'),
        aws_secret_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
        aws_bucket=os.getenv('AWS_MODEL_BUCKET', 'your-model-bucket'),
        aws_region=os.getenv('AWS_REGION', 'us-east-1'),
        
        # Google Cloud
        gcs_credentials_path=os.getenv('GOOGLE_APPLICATION_CREDENTIALS'),
        gcs_bucket=os.getenv('GCS_MODEL_BUCKET', 'your-model-bucket'),
        
        # Cache settings
        cache_dir=os.getenv('MODEL_CACHE_DIR', './models_cache'),
        max_cache_size_gb=float(os.getenv('MAX_CACHE_SIZE_GB', '10.0')),
        cache_ttl_hours=int(os.getenv('CACHE_TTL_HOURS', '24'))
    )
    
    return CloudModelManager(config)

def download_production_models() -> Dict[str, str]:
    """Download pre-built engine models for production use with separate repos"""
    
    # Get configuration
    hf_token = os.getenv('HUGGINGFACE_TOKEN')
    crowd_repo = os.getenv('HF_CROWD_REPO', 'VirtualPresenz/crowd_analytics')
    weapon_repo = os.getenv('HF_WEAPON_REPO', 'VirtualPresenz/Gundetection')
    crowd_filename = os.getenv('CROWD_MODEL_FILENAME', 'best_yolo11s_crowd.engine')
    weapon_filename = os.getenv('WEAPON_MODEL_FILENAME', 'best_yolo11x_gun.engine')
    cache_dir = os.getenv('MODEL_CACHE_DIR', './models_cache')
    
    if not hf_token:
        logging.error("HUGGINGFACE_TOKEN not set")
        return {}
    
    # Create cache directory
    os.makedirs(cache_dir, exist_ok=True)
    
    # Authenticate with Hugging Face
    try:
        login(token=hf_token)
        logging.info("✅ Hugging Face authenticated")
    except Exception as e:
        logging.error(f"❌ Hugging Face authentication failed: {e}")
        return {}
    
    model_paths = {}
    
    # Download crowd model (.engine file directly)
    try:
        logging.info(f"📤 Downloading {crowd_filename} from {crowd_repo}...")
        crowd_path = hf_hub_download(
            repo_id=crowd_repo,
            filename=crowd_filename,
            cache_dir=cache_dir
        )
        
        # Copy to expected location with proper naming
        import shutil
        local_crowd_path = os.path.join(cache_dir, 'crowd_model.engine')
        shutil.copy2(crowd_path, local_crowd_path)
        model_paths['crowd_model'] = local_crowd_path
        
        # Verify it's an engine file
        file_size = os.path.getsize(local_crowd_path) / (1024 * 1024)  # MB
        logging.info(f"✅ Downloaded crowd model from {crowd_repo} ({file_size:.1f}MB)")
        
    except Exception as e:
        logging.error(f"❌ Failed to download crowd model: {e}")
    
    # Download weapon model (.engine file directly)
    try:
        logging.info(f"📤 Downloading {weapon_filename} from {weapon_repo}...")
        weapon_path = hf_hub_download(
            repo_id=weapon_repo,
            filename=weapon_filename,
            cache_dir=cache_dir
        )
        
        # Copy to expected location with proper naming
        import shutil
        local_weapon_path = os.path.join(cache_dir, 'weapon_model.engine')
        shutil.copy2(weapon_path, local_weapon_path)
        model_paths['weapon_model'] = local_weapon_path
        
        # Verify it's an engine file
        file_size = os.path.getsize(local_weapon_path) / (1024 * 1024)  # MB
        logging.info(f"✅ Downloaded weapon model from {weapon_repo} ({file_size:.1f}MB)")
        
    except Exception as e:
        logging.error(f"❌ Failed to download weapon model: {e}")
    
    if len(model_paths) == 2:
        logging.info("🎉 All TensorRT engine models downloaded successfully!")
        logging.info("📋 Model Summary:")
        for model_name, model_path in model_paths.items():
            file_size = os.path.getsize(model_path) / (1024 * 1024)
            logging.info(f"   - {model_name}: {model_path} ({file_size:.1f}MB)")
    else:
        logging.warning(f"⚠️  Only {len(model_paths)}/2 models downloaded successfully")
    
    return model_paths