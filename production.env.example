# Production Environment Configuration
# Copy this to .env and customize for your deployment

# ============================================================================
# CLOUD MODEL CONFIGURATION
# ============================================================================

# Enable cloud model downloading (recommended for production)
USE_CLOUD_MODELS=true

# Hugging Face Configuration
HUGGINGFACE_TOKEN=hf_your_token_here
HF_MODEL_REPO=your-username/inference-models

# Model filenames in your repository
CROWD_MODEL_FILENAME=best_yolo11s_crowd.engine
WEAPON_MODEL_FILENAME=best_yolo11x_gun.engine

# ============================================================================
# CAMERA CONFIGURATION
# ============================================================================

# Camera URLs (RTSP streams)
CAM1_URL=rtsp://your-camera-1-url
CAM2_URL=rtsp://your-camera-2-url
CAM3_URL=rtsp://your-camera-3-url

# ============================================================================
# PERFORMANCE SETTINGS
# ============================================================================

# Frame processing
MAX_FPS=15
FRAME_QUEUE_SIZE=5
WORKER_THREADS=3

# GPU settings
GPU_MEMORY_FRACTION=0.7

# Detection thresholds
CROWD_CONFIDENCE=0.85
WEAPON_CONFIDENCE=0.85

# ============================================================================
# STORAGE AND CACHING
# ============================================================================

# Model cache
MODEL_CACHE_DIR=./models_cache
MAX_CACHE_SIZE_GB=10.0
CACHE_TTL_HOURS=24

# Output directories
OUTPUT_DIR=./static/detection
DETECTION_DIR=./static/assets/detection
LOGS_DIR=./logs

# File management
MAX_DETECTION_FILES=1000
FILE_CLEANUP_INTERVAL=3600

# ============================================================================
# FLASK CONFIGURATION
# ============================================================================

# Flask settings
FLASK_ENV=production
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=false
SECRET_KEY=your-super-secret-key-change-this-in-production

# ============================================================================
# MONITORING AND LOGGING
# ============================================================================

# Logging
LOG_LEVEL=INFO

# Health monitoring
HEALTH_CHECK_INTERVAL=30
METRICS_RETENTION_HOURS=24

# Resource limits
MAX_MEMORY_MB=4096
