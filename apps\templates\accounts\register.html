{% extends "layouts/base-fullscreen.html" %}

{% block title %} Sign UP {% endblock %} 

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}

    <section>
      <div class="page-header min-vh-100">
        <div class="container">
          <div class="row">
            <div class="col-6 d-lg-flex d-none h-100 my-auto pe-0 position-absolute top-0 start-0 text-center justify-content-center flex-column">
              <div class="position-relative bg-gradient-primary h-100 m-3 px-7 border-radius-lg d-flex flex-column justify-content-center" style="background-image: url('/static/assets/img/illustrations/illustration-signup.jpg'); background-size: cover;">
              </div>
            </div>
            <div class="col-xl-4 col-lg-5 col-md-7 d-flex flex-column ms-auto me-auto ms-lg-auto me-lg-5">
              <div class="card card-plain">
                <div class="card-header">
                  <h4 class="font-weight-bolder">
                    Flask Material Dashboard
                  </h4>
                  <p class="mb-0">
                    {% if msg %}
                      <span class="text-danger">{{ msg | safe }}</span>
                    {% else %}
                      Enter your email and password to register
                    {% endif %}                    
                  </p>
                </div>

                {% if success %}

                  <div class="card-footer text-center pt-0 px-lg-2 px-1">
                    <p class="mb-2 text-sm mx-auto text-center">
                      <a  href="{{ url_for('authentication_blueprint.login') }}"
                          class="btn btn-lg bg-gradient-primary btn-lg w-100 mt-4 mb-0">Sign IN</a>
                    </p>
                  </div>

                {% else %}

                  <div class="card-body">
                    <form role="form" method="post" action="">

                      {{ form.hidden_tag() }} 

                      <div class="input-group input-group-outline mb-3">
                        {{ form.username(class="form-control", placeholder="Username") }}
                      </div>
                      <div class="input-group input-group-outline mb-3">
                        {{ form.email(class="form-control", type="email", placeholder="Email") }}
                      </div>
                      <div class="input-group input-group-outline mb-3">
                        {{ form.password(class="form-control", type="password", placeholder="Password") }}
                      </div>
                      <div class="form-check form-check-info text-start ps-0">
                        <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault" checked>
                        <label class="form-check-label" for="flexCheckDefault">
                          I agree the <a href="javascript:;" class="text-dark font-weight-bolder">Terms and Conditions</a>
                        </label>
                      </div>
                      <div class="text-center">
                        <button type="submit" name="register" 
                                class="btn btn-lg bg-gradient-primary btn-lg w-100 mt-4 mb-0">Sign Up</button>
                      </div>
                    </form>
                  </div>
                  <div class="card-footer text-center pt-0 px-lg-2 px-1">
                    <p class="mb-2 text-sm mx-auto">
                      Already have an account?
                      <a href="{{ url_for('authentication_blueprint.login') }}" class="text-primary text-gradient font-weight-bold">Sign IN</a>
                    </p>
                  </div>

                {% endif %}

                
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}{% endblock javascripts %}
