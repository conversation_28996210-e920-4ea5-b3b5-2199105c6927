# TensorRT Quick Start Guide

This guide helps you quickly set up and run the TensorRT-optimized inference system.

## 🚀 Quick Start

### Prerequisites

- NVIDIA GPU with CUDA support
- TensorRT 8.x or higher
- Python 3.8+
- CUDA 11.x or 12.x

### 1. Install Dependencies

```bash
# Install TensorRT (if not already installed)
pip install tensorrt

# Install other requirements
pip install -r requirements.txt
```

### 2. Convert Models to TensorRT

```bash
# Run the setup script
python setup_tensorrt_models.py

# Or manually convert
python -c "
from ultralytics import YOLO
model = YOLO('./models/best_yolo11s_crowd.pt')
model.export(format='engine', device=0)
"
```

### 3. Configure Environment

```bash
# Copy configuration
cp tensorrt_config.env.example .env

# Edit with your settings
nano .env
```

### 4. Run the System

```bash
# Test configuration
python run_production.py --check-only

# Run inference
python run_production.py --mode standalone
```

## ⚙️ TensorRT Configuration

### Model Conversion Settings

```python
# Precision modes
FP32 = "fp32"    # Highest accuracy, slower
FP16 = "fp16"    # Good balance
INT8 = "int8"    # Fastest, requires calibration

# Workspace size (MB)
WORKSPACE_SIZE = 4096

# Optimization profiles
MIN_SHAPES = (1, 3, 640, 640)
OPT_SHAPES = (1, 3, 640, 640)
MAX_SHAPES = (1, 3, 640, 640)
```

### Environment Variables

```bash
# TensorRT model paths
CROWD_TENSORRT_MODEL=./models/best_yolo11s_crowd.engine
WEAPON_TENSORRT_MODEL=./models/best_yolo11x_gun.engine

# Fallback PyTorch models
CROWD_PT_MODEL=./models/best_yolo11s_crowd.pt
WEAPON_PT_MODEL=./models/best_yolo11x_gun.pt

# Performance settings
TENSORRT_PRECISION=fp16
TENSORRT_WORKSPACE_SIZE=4096
```

## 🔧 Model Optimization

### Precision Modes

#### FP32 (Full Precision)
- **Accuracy**: Highest
- **Speed**: Baseline
- **Memory**: Highest
- **Use case**: Development, accuracy validation

#### FP16 (Half Precision)
- **Accuracy**: ~99% of FP32
- **Speed**: 1.5-2x faster
- **Memory**: 50% less
- **Use case**: Production (recommended)

#### INT8 (Integer Precision)
- **Accuracy**: 95-98% of FP32
- **Speed**: 2-4x faster
- **Memory**: 75% less
- **Use case**: Edge deployment, high throughput

### Optimization Profiles

```python
# Single batch optimization
profiles = {
    "min": (1, 3, 640, 640),
    "opt": (1, 3, 640, 640),
    "max": (1, 3, 640, 640)
}

# Multi-batch optimization
profiles = {
    "min": (1, 3, 640, 640),
    "opt": (4, 3, 640, 640),
    "max": (8, 3, 640, 640)
}

# Dynamic input sizes
profiles = {
    "min": (1, 3, 320, 320),
    "opt": (1, 3, 640, 640),
    "max": (1, 3, 1280, 1280)
}
```

## 📊 Performance Benchmarks

### Expected Performance (RTX 4090)

| Model | Precision | Inference Time | FPS | Memory |
|-------|-----------|----------------|-----|---------|
| Crowd (YOLOv11s) | FP32 | 15ms | 66 | 2GB |
| Crowd (YOLOv11s) | FP16 | 8ms | 125 | 1GB |
| Crowd (YOLOv11s) | INT8 | 5ms | 200 | 512MB |
| Weapon (YOLOv11x) | FP32 | 45ms | 22 | 6GB |
| Weapon (YOLOv11x) | FP16 | 25ms | 40 | 3GB |
| Weapon (YOLOv11x) | INT8 | 15ms | 66 | 1.5GB |

### Optimization Tips

1. **Use FP16 for production** - Best balance of speed and accuracy
2. **Optimize for your input size** - Match your camera resolution
3. **Use dynamic batching** - Process multiple frames together
4. **Warm up the engine** - First inference is always slower

## 🛠️ Troubleshooting

### Common Issues

#### TensorRT Not Found
```bash
# Install TensorRT
pip install tensorrt

# Or use conda
conda install tensorrt -c conda-forge
```

#### CUDA Version Mismatch
```bash
# Check CUDA version
nvidia-smi

# Install matching PyTorch
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

#### Model Conversion Failed
```bash
# Check GPU memory
nvidia-smi

# Reduce workspace size
TENSORRT_WORKSPACE_SIZE=2048

# Use FP32 if FP16 fails
TENSORRT_PRECISION=fp32
```

#### Engine File Corrupted
```bash
# Delete and regenerate
rm ./models/*.engine
python setup_tensorrt_models.py
```

### Debug Commands

```bash
# Test TensorRT installation
python -c "import tensorrt; print(tensorrt.__version__)"

# Check CUDA availability
python -c "import torch; print(torch.cuda.is_available())"

# Verify model loading
python -c "
from ultralytics import YOLO
model = YOLO('./models/best_yolo11s_crowd.engine')
print('Model loaded successfully')
"
```

## 🔄 Model Updates

### Regenerating Engines

When you update your PyTorch models:

```bash
# Remove old engines
rm ./models/*.engine

# Regenerate with new models
python setup_tensorrt_models.py

# Test new engines
python run_production.py --check-only
```

### Version Management

```bash
# Backup current engines
mkdir -p ./models/backup/$(date +%Y%m%d)
cp ./models/*.engine ./models/backup/$(date +%Y%m%d)/

# Generate new engines
python setup_tensorrt_models.py

# Rollback if needed
cp ./models/backup/YYYYMMDD/*.engine ./models/
```

## 📈 Advanced Optimization

### Custom Optimization Profiles

```python
# Create custom profiles for your use case
def create_optimization_profiles():
    return {
        # Single camera profile
        "single_cam": {
            "min": (1, 3, 640, 640),
            "opt": (1, 3, 640, 640),
            "max": (1, 3, 640, 640)
        },
        # Multi-camera profile
        "multi_cam": {
            "min": (1, 3, 640, 640),
            "opt": (3, 3, 640, 640),
            "max": (6, 3, 640, 640)
        }
    }
```

### INT8 Calibration

For maximum performance with INT8:

```python
# Prepare calibration dataset
calibration_images = [
    "./calibration/image1.jpg",
    "./calibration/image2.jpg",
    # ... more images
]

# Convert with calibration
model = YOLO('./models/best_yolo11s_crowd.pt')
model.export(
    format='engine',
    int8=True,
    data='./calibration_dataset.yaml'
)
```

### Memory Optimization

```bash
# Reduce memory usage
TENSORRT_WORKSPACE_SIZE=1024
GPU_MEMORY_FRACTION=0.5

# Use memory pooling
CUDA_MEMORY_POOL_SIZE=2048
```

## 🚀 Production Deployment

### Docker with TensorRT

```dockerfile
# Use TensorRT base image
FROM nvcr.io/nvidia/tensorrt:23.08-py3

# Copy pre-built engines
COPY ./models/*.engine /app/models/

# Set TensorRT environment
ENV TENSORRT_PRECISION=fp16
ENV USE_TENSORRT=true
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: inference-tensorrt
spec:
  template:
    spec:
      containers:
      - name: inference
        image: your-registry/inference-tensorrt
        resources:
          limits:
            nvidia.com/gpu: 1
        env:
        - name: USE_TENSORRT
          value: "true"
        - name: TENSORRT_PRECISION
          value: "fp16"
```

This TensorRT setup provides maximum performance for your inference workloads!
