# Cloud Models Configuration
# For downloading TensorRT models from cloud sources

# ============================================================================
# HUGGING FACE CONFIGURATION
# ============================================================================

# Enable cloud model downloading
USE_CLOUD_MODELS=true

# Hugging Face Hub token (get from https://huggingface.co/settings/tokens)
HUGGINGFACE_TOKEN=hf_your_token_here

# Your model repository
HF_MODEL_REPO=your-username/inference-models

# Model filenames in your repository
CROWD_MODEL_FILENAME=best_yolo11s_crowd.engine
WEAPON_MODEL_FILENAME=best_yolo11x_gun.engine

# ============================================================================
# AWS S3 CONFIGURATION (Optional)
# ============================================================================

# AWS credentials
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1

# S3 bucket for models
AWS_MODEL_BUCKET=your-model-bucket

# ============================================================================
# GOOGLE CLOUD STORAGE CONFIGURATION (Optional)
# ============================================================================

# Path to service account JSON file
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# GCS bucket for models
GCS_MODEL_BUCKET=your-model-bucket

# ============================================================================
# CACHE CONFIGURATION
# ============================================================================

# Local cache directory
MODEL_CACHE_DIR=./models_cache

# Cache settings
MAX_CACHE_SIZE_GB=10.0
CACHE_TTL_HOURS=24

# ============================================================================
# CAMERA CONFIGURATION
# ============================================================================

# Camera URLs
CAM1_URL=rtsp://your-camera-1-url
CAM2_URL=rtsp://your-camera-2-url
CAM3_URL=rtsp://your-camera-3-url

# ============================================================================
# INFERENCE SETTINGS
# ============================================================================

# Detection confidence thresholds
CROWD_CONFIDENCE=0.85
WEAPON_CONFIDENCE=0.85

# Performance settings
MAX_FPS=15
WORKER_THREADS=3
GPU_MEMORY_FRACTION=0.7
