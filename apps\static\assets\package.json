{"name": "@themesberg/pixel-lite-bootstrap-ui-kit", "version": "4.0.0", "description": "Open-source Bootstrap 5 UI Kit", "main": "gulpfile.js", "author": "Themesberg", "keywords": ["bootstrap", "bootstrap 5", "bootstrap 5 ui kit", "bootstrap ui kit", "ui kit", "components", "elements", "responsive", "front-end", "css", "sass", "gulp", "web"], "homepage": "https://themesberg.com/product/ui-kit/pixel-free-bootstrap-5-ui-kit", "repository": {"type": "git", "url": "https://github.com/themesberg/pixel-bootstrap-ui-kit"}, "bugs": {"email": "<EMAIL>"}, "license": "MIT License", "devDependencies": {"browser-sync": "^2.27.4", "del": "^6.0.0", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-cssbeautify": "^3.0.0", "node-sass": "^6.0.1", "gulp-file-include": "^2.3.0", "gulp-header": "^2.0.9", "gulp-htmlmin": "^5.0.1", "gulp-npm-dist": "^1.0.3", "gulp-plumber": "^1.2.1", "gulp-rename": "^2.0.0", "gulp-sass": "^5.0.0", "gulp-sourcemaps": "^3.0.0", "gulp-uglify": "^3.0.2", "gulp-wait": "^0.0.2", "merge-stream": "^2.0.0"}}