name: Setup Development Environment on Lambda.ai

on:
  workflow_dispatch:
    inputs:
      lambda_ip:
        description: 'Lambda.ai instance IP address'
        required: true
        type: string
      lambda_user:
        description: 'SSH username'
        required: false
        default: 'ubuntu'
        type: string
      setup_type:
        description: 'Setup type'
        required: true
        default: 'development'
        type: choice
        options:
        - development
        - production
        - testing
      skip_model_download:
        description: 'Skip model download (for faster testing)'
        required: false
        default: false
        type: boolean

env:
  LAMBDA_IP: ${{ github.event.inputs.lambda_ip }}
  LAMBDA_USER: ${{ github.event.inputs.lambda_user }}
  SETUP_TYPE: ${{ github.event.inputs.setup_type }}
  SKIP_MODELS: ${{ github.event.inputs.skip_model_download }}

jobs:
  validate-inputs:
    runs-on: ubuntu-latest
    outputs:
      lambda_ip: ${{ steps.validate.outputs.lambda_ip }}
      lambda_user: ${{ steps.validate.outputs.lambda_user }}
    steps:
      - name: Validate inputs
        id: validate
        run: |
          # Validate IP address format
          if [[ ! "${{ env.LAMBDA_IP }}" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            echo "❌ Invalid IP address format: ${{ env.LAMBDA_IP }}"
            exit 1
          fi
          
          echo "✅ IP address validation passed"
          echo "lambda_ip=${{ env.LAMBDA_IP }}" >> $GITHUB_OUTPUT
          echo "lambda_user=${{ env.LAMBDA_USER }}" >> $GITHUB_OUTPUT

  setup-lambda-environment:
    needs: validate-inputs
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.LAMBDA_SSH_PRIVATE_KEY }}" > ~/.ssh/lambda_key
          chmod 600 ~/.ssh/lambda_key
          
          # Add Lambda.ai instance to known hosts
          ssh-keyscan -H ${{ env.LAMBDA_IP }} >> ~/.ssh/known_hosts

      - name: Test SSH connection
        run: |
          echo "🔗 Testing SSH connection to Lambda.ai instance..."
          ssh -i ~/.ssh/lambda_key -o ConnectTimeout=30 ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }} "echo 'SSH connection successful'"

      - name: Check Lambda.ai instance specifications
        run: |
          echo "🖥️  Checking Lambda.ai instance specifications..."
          ssh -i ~/.ssh/lambda_key ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }} << 'EOF'
            echo "=== System Information ==="
            uname -a
            echo ""
            echo "=== GPU Information ==="
            nvidia-smi || echo "No GPU detected"
            echo ""
            echo "=== Python Information ==="
            python3 --version
            echo ""
            echo "=== Disk Space ==="
            df -h /
            echo ""
            echo "=== Memory Information ==="
            free -h
          EOF

      - name: Install system dependencies on Lambda.ai
        run: |
          echo "📦 Installing system dependencies on Lambda.ai..."
          ssh -i ~/.ssh/lambda_key ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }} << 'EOF'
            sudo apt-get update
            sudo apt-get install -y \
              python3-pip \
              python3-venv \
              python3-dev \
              build-essential \
              git \
              curl \
              wget \
              ffmpeg \
              libsm6 \
              libxext6 \
              libxrender-dev \
              libglib2.0-0 \
              htop \
              tmux
            
            echo "✅ System dependencies installed"
          EOF

      - name: Transfer and run main setup script
        run: |
          echo "🚀 Running main setup using transferred files..."
          
          # Transfer the entire repository to Lambda.ai
          ssh -i ~/.ssh/lambda_key ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }} "rm -rf ~/automated-setup"
          ssh -i ~/.ssh/lambda_key ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }} "mkdir -p ~/automated-setup"
          scp -i ~/.ssh/lambda_key -r . ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }}:~/automated-setup/
          
          # Run the setup script using transferred files (no git clone needed)
          ssh -i ~/.ssh/lambda_key ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }} << 'EOF'
            cd ~/automated-setup
            
            # Make setup script executable and run it
            chmod +x setup_environment.sh
            ./setup_environment.sh
            
            echo "✅ Main setup script completed"
          EOF

      - name: Run Lambda.ai post-setup configuration
        run: |
          echo "⚙️  Running Lambda.ai post-setup configuration..."
          
          # Transfer the post-setup script
          scp -i ~/.ssh/lambda_key .github/scripts/lambda-post-setup.sh ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }}:~/lambda-post-setup.sh
          
          # Run the post-setup script with environment variables
          ssh -i ~/.ssh/lambda_key ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }} << 'EOF'
            export HUGGINGFACE_TOKEN="${{ secrets.HUGGINGFACE_TOKEN }}"
            export PROJECT_DIR="$HOME/automated-setup"
            
            chmod +x ~/lambda-post-setup.sh
            ~/lambda-post-setup.sh
            
            # Clean up the script
            rm -f ~/lambda-post-setup.sh
          EOF

      - name: Test Flask application startup (includes model loading)
        if: ${{ env.SKIP_MODELS != 'true' }}
        run: |
          echo "🌐 Testing Flask application startup and model loading..."
          ssh -i ~/.ssh/lambda_key ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }} << 'EOF'
            cd ~/automated-setup
            source env/bin/activate
            
            # Set Flask environment variables
            export FLASK_APP=run.py
            export FLASK_ENV=development
            export HUGGINGFACE_TOKEN="${{ secrets.HUGGINGFACE_TOKEN }}"
            
            # Start Flask app in background and test startup
            echo "🚀 Starting Flask application..."
            timeout 30 flask run --host=0.0.0.0 --port=5000 &
            FLASK_PID=$!
            
            # Wait for app to start
            sleep 15
            
            # Test if app is responding
            if curl -s http://localhost:5000 > /dev/null; then
                echo "✅ Flask application started successfully"
                echo "✅ Models loaded successfully (if startup completed)"
            else
                echo "⚠️  Flask application may still be loading models..."
            fi
            
            # Clean up
            kill $FLASK_PID 2>/dev/null || true
            
            echo "✅ Flask application validation complete"
          EOF



      - name: Final validation and cleanup
        run: |
          echo "🔍 Running final validation..."
          ssh -i ~/.ssh/lambda_key ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }} << 'EOF'
            cd ~/automated-setup
            
            # Run status check
            ./check_status.sh
            
            echo ""
            echo "🎉 LAMBDA.AI SETUP COMPLETE!"
            echo "   Instance IP: ${{ env.LAMBDA_IP }}"
            echo "   Access URL: http://${{ env.LAMBDA_IP }}:5000"
            echo "   Setup used: existing setup_environment.sh"
            echo "   Instance status: RUNNING (will remain active)"
            echo ""
            echo "📋 Next steps:"
            echo "   1. SSH to instance: ssh ${{ env.LAMBDA_USER }}@${{ env.LAMBDA_IP }}"
            echo "   2. Start application: cd ~/automated-setup && ./start_app.sh"
            echo "   3. Check status anytime: ./check_status.sh"
          EOF
          
          # Cleanup local SSH key
          rm -f ~/.ssh/lambda_key
          
          echo ""
          echo "✅ GitHub Actions workflow completed successfully"
          echo "🖥️  Lambda.ai instance remains running and ready for development"