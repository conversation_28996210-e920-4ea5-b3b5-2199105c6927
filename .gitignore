# byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# tests and coverage
*.pytest_cache
.coverage

# database & logs
*.db
*.sqlite3
*.log

# venv
env
venv
dashboard

# other
.DS_Store

# idea
.idea

# sphinx docs 
_build
_static
_templates

# javascript
package-lock.json
.vscode/symbols.json

apps/static/assets/node_modules
apps/static/assets/yarn.lock
apps/static/assets/.temp
apps/home/<USER>
