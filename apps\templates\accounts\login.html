{% extends "layouts/base-fullscreen.html" %}

{% block title %} Sign IN {% endblock %} 

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}
    
  <div class="page-header align-items-start min-vh-100" 
        style="background-image: url('https://images.unsplash.com/photo-1497294815431-9365093b7331?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1950&q=80');">
    <span class="mask bg-gradient-dark opacity-6"></span>
    <div class="container my-auto">
      <div class="row">
        <div class="col-lg-4 col-md-8 col-12 mx-auto">
          <div class="card z-index-0 fadeIn3 fadeInBottom">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
              <div class="bg-gradient-primary shadow-primary border-radius-lg py-3 pe-1">
                <h4 class="text-white font-weight-bolder text-center mt-2 mb-0">
                  Virtual Presenz
                </h4>
                <div class="row mt-3">
                  <p class="mb-0 text-white text-center">
                    {% if msg %}
                      {{ msg | safe }}
                    {% else %}
                      Add your credentials
                    {% endif %}
                  </p>
                </div>
              </div>
            </div>
            <div class="card-body">
              
              <form role="form" method="post" action="" class="text-start">

                {{ form.hidden_tag() }} 

                <div class="input-group input-group-outline mb-3">
                    {{ form.username(class="form-control", placeholder="Username") }}
                </div>
                <div class="input-group input-group-outline mb-3">
                  {{ form.password(class="form-control", type="password", placeholder="Password") }}
                </div>
                <div class="form-check form-switch d-flex align-items-center mb-3">
                  <input class="form-check-input" type="checkbox" id="rememberMe">
                  <label class="form-check-label mb-0 ms-2" for="rememberMe">Remember me</label>
                </div>
                <div class="text-center">
                  <button type="submit" name="login" 
                          class="btn bg-gradient-primary w-100 my-4 mb-2">Sign in</button>
                </div>
                <p class="mt-4 text-sm text-center">
                  Don't have an account?
                  <a href="{{ url_for('authentication_blueprint.register') }}" class="text-primary text-gradient font-weight-bold">Sign UP</a>
                </p>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    {% include 'includes/footer-fullscreen.html' %}

  </div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}{% endblock javascripts %}
