# Cloud Models Guide

This guide explains how to use cloud-based model storage for the Integrated Weapon and Crowd Detection System.

## 🌐 Overview

Cloud models enable:
- ✅ **Automatic model downloading** from cloud repositories
- ✅ **Smaller Docker images** (no embedded models)
- ✅ **Easy model updates** without rebuilding containers
- ✅ **Version control** for models
- ✅ **Scalable deployments** across multiple instances

## 🚀 Quick Setup

### 1. Upload Models to Hugging Face

```bash
# Install Hugging Face CLI
pip install huggingface_hub

# Login to Hugging Face
huggingface-cli login

# Create repository
huggingface-cli repo create your-username/inference-models

# Upload models
huggingface-cli upload your-username/inference-models ./models/best_yolo11s_crowd.engine
huggingface-cli upload your-username/inference-models ./models/best_yolo11x_gun.engine
```

### 2. Configure Environment

```bash
# Enable cloud models
USE_CLOUD_MODELS=true

# Set Hugging Face credentials
HUGGINGFACE_TOKEN=hf_your_token_here
HF_MODEL_REPO=your-username/inference-models

# Model filenames
CROWD_MODEL_FILENAME=best_yolo11s_crowd.engine
WEAPON_MODEL_FILENAME=best_yolo11x_gun.engine
```

### 3. Test Download

```bash
python run_production.py --check-only
```

## 🔧 Supported Cloud Providers

### Hugging Face Hub (Recommended)

**Pros:**
- Free for public models
- Easy integration
- Version control
- Community support

**Setup:**
```bash
HUGGINGFACE_TOKEN=hf_your_token
HF_MODEL_REPO=your-username/models
```

### AWS S3

**Pros:**
- Enterprise-grade
- High availability
- Fine-grained access control

**Setup:**
```bash
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_MODEL_BUCKET=your-model-bucket
AWS_REGION=us-east-1
```

### Google Cloud Storage

**Pros:**
- Google Cloud integration
- Global distribution
- Advanced security

**Setup:**
```bash
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json
GCS_MODEL_BUCKET=your-model-bucket
```

### Direct URLs

**Pros:**
- Simple setup
- Any HTTP/HTTPS source
- Custom CDN support

**Setup:**
```bash
CROWD_MODEL_URL=https://your-cdn.com/crowd_model.engine
WEAPON_MODEL_URL=https://your-cdn.com/weapon_model.engine
```

## 📁 Model Repository Structure

### Hugging Face Repository

```
your-username/inference-models/
├── README.md
├── best_yolo11s_crowd.engine    # Crowd detection model
├── best_yolo11x_gun.engine      # Weapon detection model
├── model_info.json              # Model metadata
└── versions/                    # Version history
    ├── v1.0/
    ├── v1.1/
    └── latest/
```

### Model Metadata (model_info.json)

```json
{
  "models": {
    "crowd_detection": {
      "filename": "best_yolo11s_crowd.engine",
      "version": "1.0",
      "size_mb": 47.8,
      "accuracy": 0.92,
      "created": "2024-01-15T10:30:00Z"
    },
    "weapon_detection": {
      "filename": "best_yolo11x_gun.engine",
      "version": "1.0",
      "size_mb": 284.0,
      "accuracy": 0.95,
      "created": "2024-01-15T10:30:00Z"
    }
  }
}
```

## ⚙️ Configuration Options

### Cache Settings

```bash
# Cache directory
MODEL_CACHE_DIR=./models_cache

# Cache size limit (GB)
MAX_CACHE_SIZE_GB=10.0

# Cache TTL (hours)
CACHE_TTL_HOURS=24
```

### Download Priority

Models are downloaded in this order:
1. **Hugging Face Hub** (if configured)
2. **AWS S3** (if configured)
3. **Google Cloud Storage** (if configured)
4. **Direct URLs** (if configured)

### Fallback Strategy

```bash
# Enable local fallback
USE_LOCAL_FALLBACK=true

# Local model paths
CROWD_MODEL_PATH=./models/best_yolo11s_crowd.engine
WEAPON_MODEL_PATH=./models/best_yolo11x_gun.engine
```

## 🔄 Model Updates

### Automatic Updates

Models are automatically re-downloaded when:
- Cache TTL expires (default: 24 hours)
- Model file is corrupted or missing
- Application restart with `FORCE_MODEL_DOWNLOAD=true`

### Manual Updates

```bash
# Force model re-download
docker exec inference-app python -c "
from apps.home.cloud_model_manager import download_production_models
download_production_models()
"

# Clear cache
rm -rf ./models_cache/*
```

### Version Management

```bash
# Use specific model version
HF_MODEL_REPO=your-username/models
HF_MODEL_REVISION=v1.1

# Use latest version (default)
HF_MODEL_REVISION=main
```

## 🚀 Deployment Scenarios

### Scenario 1: Development

```bash
# Use local models for development
USE_CLOUD_MODELS=false
CROWD_MODEL_PATH=./models/crowd_model.engine
WEAPON_MODEL_PATH=./models/weapon_model.engine
```

### Scenario 2: Staging

```bash
# Use cloud models with frequent updates
USE_CLOUD_MODELS=true
CACHE_TTL_HOURS=1
HUGGINGFACE_TOKEN=hf_staging_token
HF_MODEL_REPO=your-username/models-staging
```

### Scenario 3: Production

```bash
# Use cloud models with stable caching
USE_CLOUD_MODELS=true
CACHE_TTL_HOURS=24
HUGGINGFACE_TOKEN=hf_production_token
HF_MODEL_REPO=your-username/models-production
```

## 🔒 Security Best Practices

### Token Management

```bash
# Use environment-specific tokens
HUGGINGFACE_TOKEN_DEV=hf_dev_token
HUGGINGFACE_TOKEN_PROD=hf_prod_token

# Rotate tokens regularly
# Set token expiration dates
```

### Access Control

```bash
# Private repositories
HF_MODEL_REPO=your-username/private-models

# Team access only
# Configure repository permissions
```

### Network Security

```bash
# Use HTTPS only
VERIFY_SSL=true

# Proxy support
HTTP_PROXY=http://proxy.company.com:8080
HTTPS_PROXY=http://proxy.company.com:8080
```

## 📊 Monitoring

### Download Metrics

Monitor model download performance:
- Download time
- Success/failure rates
- Cache hit rates
- Network usage

### Health Checks

```bash
# Check model availability
curl http://localhost:5000/api/models/status

# Verify cache status
curl http://localhost:5000/api/cache/status
```

### Logging

```bash
# Enable detailed logging
LOG_LEVEL=DEBUG

# Model-specific logs
tail -f logs/inference.log | grep "model"
```

## 🛠️ Troubleshooting

### Common Issues

#### Authentication Failed
```bash
# Verify token
huggingface-cli whoami

# Test repository access
huggingface-cli repo info your-username/models
```

#### Download Timeout
```bash
# Increase timeout
MODEL_DOWNLOAD_TIMEOUT=300

# Use different region/mirror
HF_ENDPOINT=https://hf-mirror.com
```

#### Cache Issues
```bash
# Clear corrupted cache
rm -rf ./models_cache/*

# Disable cache temporarily
CACHE_TTL_HOURS=0
```

#### Network Issues
```bash
# Test connectivity
curl -I https://huggingface.co

# Check proxy settings
echo $HTTP_PROXY
```

### Debug Commands

```bash
# Test model download
python -c "
from apps.home.cloud_model_manager import download_production_models
models = download_production_models()
print(f'Downloaded: {models}')
"

# Check cache status
python -c "
import os
cache_dir = './models_cache'
if os.path.exists(cache_dir):
    files = os.listdir(cache_dir)
    print(f'Cache files: {files}')
else:
    print('Cache directory not found')
"
```

## 📈 Performance Optimization

### Parallel Downloads

```bash
# Enable concurrent downloads
CONCURRENT_DOWNLOADS=3

# Optimize chunk size
DOWNLOAD_CHUNK_SIZE=8192
```

### CDN Integration

```bash
# Use CDN for faster downloads
HF_ENDPOINT=https://cdn.huggingface.co
```

### Regional Optimization

```bash
# Use regional endpoints
AWS_REGION=us-west-2  # Closer to your deployment
GCS_REGION=us-central1
```

## 🔄 Migration Guide

### From Local to Cloud Models

1. **Upload existing models**:
```bash
huggingface-cli upload your-username/models ./models/
```

2. **Update configuration**:
```bash
USE_CLOUD_MODELS=true
HUGGINGFACE_TOKEN=your_token
HF_MODEL_REPO=your-username/models
```

3. **Test deployment**:
```bash
python run_production.py --check-only
```

4. **Deploy**:
```bash
docker-compose up -d
```

### Between Cloud Providers

1. **Download from current provider**
2. **Upload to new provider**
3. **Update configuration**
4. **Test and deploy**

This cloud models system provides flexibility, scalability, and ease of deployment for your inference system!
