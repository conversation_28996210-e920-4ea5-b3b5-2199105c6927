"""
Production-ready inference system for hosting
Optimized for reliability, scalability, and monitoring
"""

import cv2
import time
import threading
import json
import os
import logging
import queue
import signal
import sys
from datetime import datetime
from typing import Dict, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from contextlib import contextmanager
import numpy as np
import torch
from ultralytics import YOLO
from collections import defaultdict, deque
import psutil
import gc

# Production Configuration
@dataclass
class ProductionConfig:
    """Production configuration with validation"""
    
    # Model paths (will be updated if cloud models are used)
    crowd_model_path: str = os.getenv('CROWD_MODEL_PATH', './models/best_yolo11s_crowd.engine')
    weapon_model_path: str = os.getenv('WEAPON_MODEL_PATH', './models/best_yolo11x_gun.engine')
    
    # Camera configuration
    camera_urls: Dict[str, str] = None
    
    # Performance settings
    max_fps: int = int(os.getenv('MAX_FPS', '15'))  # Reduced for production
    frame_queue_size: int = int(os.getenv('FRAME_QUEUE_SIZE', '5'))
    worker_threads: int = int(os.getenv('WORKER_THREADS', '3'))
    
    # Detection thresholds
    crowd_confidence: float = float(os.getenv('CROWD_CONFIDENCE', '0.85'))
    weapon_confidence: float = float(os.getenv('WEAPON_CONFIDENCE', '0.85'))
    
    # Resource limits
    max_memory_mb: int = int(os.getenv('MAX_MEMORY_MB', '4096'))
    gpu_memory_fraction: float = float(os.getenv('GPU_MEMORY_FRACTION', '0.7'))
    
    # Monitoring
    health_check_interval: int = int(os.getenv('HEALTH_CHECK_INTERVAL', '30'))
    metrics_retention_hours: int = int(os.getenv('METRICS_RETENTION_HOURS', '24'))
    
    # Storage
    output_dir: str = os.getenv('OUTPUT_DIR', '/app/static/detection')
    detection_dir: str = os.getenv('DETECTION_DIR', '/app/static/assets/detection')
    logs_dir: str = os.getenv('LOGS_DIR', '/app/logs')
    
    # Security
    max_detection_files: int = int(os.getenv('MAX_DETECTION_FILES', '1000'))
    file_cleanup_interval: int = int(os.getenv('FILE_CLEANUP_INTERVAL', '3600'))
    
    def __post_init__(self):
        if self.camera_urls is None:
            self.camera_urls = {
                "cam1": os.getenv('CAM1_URL', "rtsp://virtualpresenz.duckdns.org:8554/stream0"),
                "cam2": os.getenv('CAM2_URL', "rtsp://virtualpresenz2.duckdns.org:8553/stream0"),
                "cam3": os.getenv('CAM3_URL', "rtsp://virtualpresenz1.duckdns.org:8555/stream0")
            }

        # Enable test mode if cameras are not available
        self.test_mode = os.getenv('TEST_MODE', 'false').lower() == 'true'
        
        # Create directories
        for directory in [self.output_dir, self.detection_dir, self.logs_dir]:
            os.makedirs(directory, exist_ok=True)
        
        # Validate configuration
        self._validate()
    
    def _validate(self):
        """Validate configuration"""
        errors = []
        
        # Only check model files if not using cloud models
        use_cloud_models = os.getenv('USE_CLOUD_MODELS', 'false').lower() == 'true'
        
        if not use_cloud_models:
            # Check model files only for local models
            if not os.path.exists(self.crowd_model_path):
                errors.append(f"Crowd model not found: {self.crowd_model_path}")
            if not os.path.exists(self.weapon_model_path):
                errors.append(f"Weapon model not found: {self.weapon_model_path}")
        
        # Check thresholds
        if not 0.1 <= self.crowd_confidence <= 1.0:
            errors.append("Crowd confidence must be between 0.1 and 1.0")
        if not 0.1 <= self.weapon_confidence <= 1.0:
            errors.append("Weapon confidence must be between 0.1 and 1.0")
        
        if errors:
            raise ValueError(f"Configuration errors: {'; '.join(errors)}")

# Production logging setup
def setup_production_logging(config: ProductionConfig):
    """Setup production-grade logging"""
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(simple_formatter)
    console_handler.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    
    # File handler for detailed logs
    log_file = os.path.join(config.logs_dir, 'inference.log')
    file_handler = logging.handlers.RotatingFileHandler(
        log_file, maxBytes=10*1024*1024, backupCount=5
    )
    file_handler.setFormatter(detailed_formatter)
    file_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)
    
    # Error file handler
    error_file = os.path.join(config.logs_dir, 'errors.log')
    error_handler = logging.handlers.RotatingFileHandler(
        error_file, maxBytes=5*1024*1024, backupCount=3
    )
    error_handler.setFormatter(detailed_formatter)
    error_handler.setLevel(logging.ERROR)
    root_logger.addHandler(error_handler)
    
    return logging.getLogger(__name__)

# Production metrics and monitoring
class ProductionMetrics:
    """Production metrics collection and monitoring"""
    
    def __init__(self, config: ProductionConfig):
        self.config = config
        self.metrics = defaultdict(lambda: deque(maxlen=1000))
        self.lock = threading.Lock()
        self.start_time = time.time()
        
    def record_metric(self, name: str, value: float, timestamp: Optional[float] = None):
        """Record a metric value"""
        if timestamp is None:
            timestamp = time.time()
        
        with self.lock:
            self.metrics[name].append((timestamp, value))
    
    def get_metrics(self, name: str, duration_seconds: int = 300) -> list:
        """Get metrics for the last N seconds"""
        cutoff = time.time() - duration_seconds
        with self.lock:
            return [(ts, val) for ts, val in self.metrics[name] if ts >= cutoff]
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health metrics"""
        process = psutil.Process()
        
        return {
            'uptime_seconds': time.time() - self.start_time,
            'cpu_percent': process.cpu_percent(),
            'memory_mb': process.memory_info().rss / 1024 / 1024,
            'gpu_available': torch.cuda.is_available(),
            'gpu_memory_mb': torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 if torch.cuda.is_available() else 0,
            'active_threads': threading.active_count(),
            'timestamp': time.time()
        }

# Production-ready camera manager
class ProductionCameraManager:
    """Production camera management with reconnection and monitoring"""
    
    def __init__(self, config: ProductionConfig, metrics: ProductionMetrics):
        self.config = config
        self.metrics = metrics
        self.logger = logging.getLogger(f"{__name__}.CameraManager")
        
        self.cameras = {}
        self.frame_queues = {}
        self.camera_threads = {}
        self.running = False
        
        # Initialize frame queues
        for cam_id in config.camera_urls.keys():
            self.frame_queues[cam_id] = queue.Queue(maxsize=config.frame_queue_size)
    
    def start(self):
        """Start all camera threads"""
        self.running = True
        
        for cam_id, url in self.config.camera_urls.items():
            thread = threading.Thread(
                target=self._camera_worker,
                args=(cam_id, url),
                name=f"Camera-{cam_id}",
                daemon=True
            )
            thread.start()
            self.camera_threads[cam_id] = thread
            self.logger.info(f"Started camera thread for {cam_id}")
    
    def stop(self):
        """Stop all camera threads"""
        self.running = False

        # Close all cameras (thread-safe iteration)
        cameras_to_close = list(self.cameras.items())
        for cam_id, cap in cameras_to_close:
            if cap and cap.isOpened():
                cap.release()

        self.cameras.clear()
        self.logger.info("Camera manager stopped")
    
    def _camera_worker(self, cam_id: str, url: str):
        """Camera worker thread with reconnection logic"""
        reconnect_delay = 5
        max_reconnect_delay = 60
        
        while self.running:
            cap = None
            try:
                self.logger.info(f"Connecting to camera {cam_id}: {url}")
                cap = cv2.VideoCapture(url)
                
                if not cap.isOpened():
                    raise Exception(f"Failed to open camera {cam_id}")
                
                # Configure camera with error handling
                try:
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    cap.set(cv2.CAP_PROP_FPS, self.config.max_fps)
                    # Set timeout for RTSP streams
                    cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
                    cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
                except Exception as e:
                    self.logger.warning(f"Failed to configure camera {cam_id}: {e}")
                
                self.cameras[cam_id] = cap
                reconnect_delay = 5  # Reset delay on successful connection
                
                # Frame capture loop
                frame_count = 0
                last_fps_time = time.time()
                
                while self.running:
                    try:
                        ret, frame = cap.read()
                        if not ret:
                            self.logger.warning(f"Camera {cam_id} frame read failed")
                            break
                    except Exception as e:
                        self.logger.error(f"Camera {cam_id} read exception: {e}")
                        break
                    
                    # Add frame to queue (non-blocking)
                    try:
                        self.frame_queues[cam_id].put_nowait((frame, time.time()))
                        frame_count += 1
                        
                        # Calculate FPS
                        if frame_count % 30 == 0:
                            current_time = time.time()
                            fps = 30 / (current_time - last_fps_time)
                            self.metrics.record_metric(f'camera_{cam_id}_fps', fps)
                            last_fps_time = current_time
                            
                    except queue.Full:
                        # Drop frame if queue is full
                        self.metrics.record_metric(f'camera_{cam_id}_dropped_frames', 1)
                    
                    # Control frame rate
                    time.sleep(1.0 / self.config.max_fps)
                    
            except Exception as e:
                self.logger.error(f"Camera {cam_id} error: {e}")
                self.metrics.record_metric(f'camera_{cam_id}_errors', 1)
                
            finally:
                if cap:
                    cap.release()
                    self.cameras.pop(cam_id, None)
            
            if self.running:
                self.logger.info(f"Reconnecting to camera {cam_id} in {reconnect_delay}s")
                time.sleep(reconnect_delay)
                reconnect_delay = min(reconnect_delay * 1.5, max_reconnect_delay)
    
    def get_latest_frame(self, cam_id: str) -> Optional[Tuple[np.ndarray, float]]:
        """Get the latest frame from a camera"""
        try:
            # Get the most recent frame, discarding older ones
            frame_data = None
            while not self.frame_queues[cam_id].empty():
                try:
                    frame_data = self.frame_queues[cam_id].get_nowait()
                except queue.Empty:
                    break
            return frame_data
        except Exception as e:
            self.logger.error(f"Error getting frame from {cam_id}: {e}")
            return None

# Production model manager
# Production model manager
class ProductionModelManager:
    """Production model management with memory optimization"""
    
    def __init__(self, config: ProductionConfig, metrics: ProductionMetrics):
        self.config = config
        self.metrics = metrics
        self.logger = logging.getLogger(f"{__name__}.ModelManager")
        
        self.crowd_model = None
        self.weapon_model = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # GPU memory management
        if torch.cuda.is_available():
            torch.cuda.set_per_process_memory_fraction(config.gpu_memory_fraction)
    
    def load_models(self) -> bool:
        """Load models with error handling and optimization"""
        try:
            self.logger.info("Loading production models...")
            
            # Try cloud download first if enabled
            use_cloud_models = os.getenv('USE_CLOUD_MODELS', 'true').lower() == 'true'
            
            if use_cloud_models:
                self.logger.info("🌐 Cloud models enabled - downloading TensorRT engines...")
                model_paths = self._download_cloud_models()
                if model_paths:
                    self.config.crowd_model_path = model_paths.get('crowd_model', self.config.crowd_model_path)
                    self.config.weapon_model_path = model_paths.get('weapon_model', self.config.weapon_model_path)
                    self.logger.info("✅ Cloud models downloaded and configured")
                else:
                    self.logger.warning("⚠️  Cloud model download failed, falling back to local models")
            else:
                self.logger.info("📁 Using local model files...")
            
            # Load crowd model
            if os.path.exists(self.config.crowd_model_path):
                self.crowd_model = YOLO(self.config.crowd_model_path)
                # Check if it's a TensorRT engine
                if self.config.crowd_model_path.endswith('.engine'):
                    self.logger.info(f"✅ Loaded crowd TensorRT engine: {self.config.crowd_model_path}")
                else:
                    self.logger.info(f"✅ Loaded crowd PyTorch model: {self.config.crowd_model_path}")
            else:
                self.logger.error(f"Crowd model not found: {self.config.crowd_model_path}")
                return False
            
            # Load weapon model
            if os.path.exists(self.config.weapon_model_path):
                self.weapon_model = YOLO(self.config.weapon_model_path)
                # Check if it's a TensorRT engine
                if self.config.weapon_model_path.endswith('.engine'):
                    self.logger.info(f"✅ Loaded weapon TensorRT engine: {self.config.weapon_model_path}")
                else:
                    self.logger.info(f"✅ Loaded weapon PyTorch model: {self.config.weapon_model_path}")
            else:
                self.logger.error(f"Weapon model not found: {self.config.weapon_model_path}")
                return False
            
            # Warm up models
            self._warmup_models()
            
            self.logger.info("🚀 All models loaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load models: {e}")
            return False
    
    def _download_cloud_models(self) -> Optional[Dict[str, str]]:
        """Download models from cloud sources"""
        try:
            from .cloud_model_manager import download_production_models
            self.logger.info("🔍 Attempting to download TensorRT engines from cloud...")
            
            model_paths = download_production_models()
            
            if model_paths:
                self.logger.info(f"✅ Downloaded {len(model_paths)} TensorRT engines from cloud")
                # Log model details
                for model_name, model_path in model_paths.items():
                    if os.path.exists(model_path):
                        file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
                        self.logger.info(f"   - {model_name}: {file_size:.1f}MB")
                    else:
                        self.logger.warning(f"   - {model_name}: File not found after download")
                return model_paths
            else:
                self.logger.warning("No models downloaded from cloud, using local paths")
                return None
                
        except ImportError:
            self.logger.warning("Cloud model manager not available, using local models")
            return None
        except Exception as e:
            self.logger.error(f"Failed to download cloud models: {e}")
            return None
    
    def _warmup_models(self):
        """Warm up models with dummy inference"""
        try:
            self.logger.info("🔥 Warming up models...")
            dummy_frame = np.zeros((640, 640, 3), dtype=np.uint8)
            
            if self.crowd_model:
                start_time = time.time()
                self.crowd_model(dummy_frame, verbose=False)
                warmup_time = (time.time() - start_time) * 1000
                self.logger.info(f"Crowd model warmed up ({warmup_time:.1f}ms)")
            
            if self.weapon_model:
                start_time = time.time()
                self.weapon_model(dummy_frame, verbose=False)
                warmup_time = (time.time() - start_time) * 1000
                self.logger.info(f"Weapon model warmed up ({warmup_time:.1f}ms)")
                
        except Exception as e:
            self.logger.warning(f"Model warmup failed: {e}")
    
    @contextmanager
    def inference_context(self):
        """Context manager for inference with memory cleanup"""
        try:
            yield
        finally:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
    
    def process_crowd_detection(self, frame: np.ndarray) -> Tuple[int, np.ndarray]:
        """Process crowd detection with optimization"""
        if self.crowd_model is None:
            return 0, frame
        
        try:
            with self.inference_context():
                start_time = time.time()
                results = self.crowd_model(frame, verbose=False)
                inference_time = time.time() - start_time
                
                self.metrics.record_metric('crowd_inference_time_ms', inference_time * 1000)
                
                people_count = 0
                for result in results:
                    for box in result.boxes:
                        conf = float(box.conf[0])
                        if conf >= self.config.crowd_confidence:
                            people_count += 1
                            x1, y1, x2, y2 = map(int, box.xyxy[0])
                            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 255), 2)
                            cv2.putText(frame, f"Person {conf:.2f}", (x1, y1 - 10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
                
                self.metrics.record_metric('people_detected', people_count)
                return people_count, frame
                
        except Exception as e:
            self.logger.error(f"Crowd detection error: {e}")
            self.metrics.record_metric('crowd_detection_errors', 1)
            return 0, frame
    
    def process_weapon_detection(self, frame: np.ndarray, cam_id: str) -> Tuple[bool, np.ndarray]:
        """Process weapon detection with optimization"""
        if self.weapon_model is None:
            return False, frame
        
        try:
            with self.inference_context():
                start_time = time.time()
                results = self.weapon_model(frame, verbose=False)
                inference_time = time.time() - start_time
                
                self.metrics.record_metric('weapon_inference_time_ms', inference_time * 1000)
                
                detected = False
                for result in results:
                    for box in result.boxes:
                        conf = float(box.conf[0])
                        class_id = int(box.cls[0])
                        
                        if class_id == 0 and conf >= self.config.weapon_confidence:
                            detected = True
                            x1, y1, x2, y2 = map(int, box.xyxy[0])
                            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 3)
                            cv2.putText(frame, f"WEAPON {conf:.2f}", (x1, y1 - 10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                
                if detected:
                    self.metrics.record_metric('weapons_detected', 1)
                    self.logger.warning(f"🚨 WEAPON DETECTED in {cam_id}")
                
                return detected, frame
                
        except Exception as e:
            self.logger.error(f"Weapon detection error: {e}")
            self.metrics.record_metric('weapon_detection_errors', 1)
            return False, frame
# Production data manager
class ProductionDataManager:
    """Production data management with persistence and cleanup"""

    def __init__(self, config: ProductionConfig, metrics: ProductionMetrics):
        self.config = config
        self.metrics = metrics
        self.logger = logging.getLogger(f"{__name__}.DataManager")

        # Thread-safe data structures
        self.lock = threading.RLock()
        self.camera_stats = {
            cam_id: {"people_count": 0, "entries": 0, "exits": 0, "last_update": 0}
            for cam_id in config.camera_urls.keys()
        }
        self.detection_data = defaultdict(list)
        self.latest_frames = {}
        self.last_detection_time = {}

        # Start cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        self.cleanup_thread.start()

    def update_camera_stats(self, cam_id: str, people_count: int):
        """Update camera statistics"""
        with self.lock:
            self.camera_stats[cam_id]["people_count"] = people_count
            self.camera_stats[cam_id]["last_update"] = time.time()

    def add_detection(self, cam_id: str, detection_data: dict):
        """Add weapon detection with rate limiting"""
        current_time = time.time()

        with self.lock:
            # Rate limiting: max 1 detection per 2 seconds per camera
            last_time = self.last_detection_time.get(cam_id, 0)
            if current_time - last_time < 2.0:
                return False

            self.last_detection_time[cam_id] = current_time
            self.detection_data[cam_id].append({
                **detection_data,
                "timestamp": current_time,
                "formatted_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })

            # Limit detection history per camera
            if len(self.detection_data[cam_id]) > 100:
                self.detection_data[cam_id] = self.detection_data[cam_id][-50:]

            return True

    def save_detection_image(self, cam_id: str, frame: np.ndarray) -> Optional[str]:
        """Save detection image with proper naming and cleanup"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{cam_id}_weapon_{timestamp}.jpg"
            filepath = os.path.join(self.config.detection_dir, filename)

            # Save image
            success = cv2.imwrite(filepath, frame, [cv2.IMWRITE_JPEG_QUALITY, 85])

            if success:
                self.logger.info(f"Saved detection image: {filename}")
                return f"/static/assets/detection/{filename}"
            else:
                self.logger.error(f"Failed to save detection image: {filename}")
                return None

        except Exception as e:
            self.logger.error(f"Error saving detection image: {e}")
            return None

    def update_frame(self, cam_id: str, frame: np.ndarray):
        """Update latest frame for streaming"""
        with self.lock:
            self.latest_frames[cam_id] = frame.copy()

    def get_camera_stats(self) -> dict:
        """Get camera statistics"""
        with self.lock:
            return {cam_id: stats.copy() for cam_id, stats in self.camera_stats.items()}

    def get_detection_data(self) -> dict:
        """Get detection data"""
        with self.lock:
            return {cam_id: detections.copy() for cam_id, detections in self.detection_data.items()}

    def get_latest_frame(self, cam_id: str) -> Optional[np.ndarray]:
        """Get latest frame for streaming"""
        with self.lock:
            return self.latest_frames.get(cam_id)

    def _cleanup_worker(self):
        """Background cleanup worker"""
        while True:
            try:
                time.sleep(self.config.file_cleanup_interval)
                self._cleanup_old_files()
            except Exception as e:
                self.logger.error(f"Cleanup worker error: {e}")

    def _cleanup_old_files(self):
        """Clean up old detection files"""
        try:
            detection_files = []
            for filename in os.listdir(self.config.detection_dir):
                if filename.endswith('.jpg'):
                    filepath = os.path.join(self.config.detection_dir, filename)
                    stat = os.stat(filepath)
                    detection_files.append((filepath, stat.st_mtime))

            # Sort by modification time (oldest first)
            detection_files.sort(key=lambda x: x[1])

            # Remove excess files
            if len(detection_files) > self.config.max_detection_files:
                files_to_remove = len(detection_files) - self.config.max_detection_files
                for filepath, _ in detection_files[:files_to_remove]:
                    try:
                        os.remove(filepath)
                        self.logger.debug(f"Removed old detection file: {filepath}")
                    except Exception as e:
                        self.logger.error(f"Failed to remove file {filepath}: {e}")

                self.logger.info(f"Cleaned up {files_to_remove} old detection files")

        except Exception as e:
            self.logger.error(f"File cleanup error: {e}")

# Main production inference system
class ProductionInferenceSystem:
    """Main production inference system orchestrator"""

    def __init__(self, config: ProductionConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.InferenceSystem")

        # Initialize components
        self.metrics = ProductionMetrics(config)
        self.camera_manager = ProductionCameraManager(config, self.metrics)
        self.model_manager = ProductionModelManager(config, self.metrics)
        self.data_manager = ProductionDataManager(config, self.metrics)

        # Processing control
        self.running = False
        self.worker_threads = []

        # Health monitoring
        self.health_monitor_thread = None

        # Graceful shutdown (only register in main thread)
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except ValueError:
            # Signal handlers can only be registered in main thread
            self.logger.info("Signal handlers not registered (not in main thread)")

    def start(self) -> bool:
        """Start the production inference system"""
        try:
            self.logger.info("🚀 Starting Production Inference System")

            # Load models
            if not self.model_manager.load_models():
                self.logger.error("Failed to load models")
                return False

            # Start camera manager
            self.camera_manager.start()

            # Start processing workers
            self.running = True
            for i in range(self.config.worker_threads):
                worker = threading.Thread(
                    target=self._processing_worker,
                    name=f"ProcessingWorker-{i}",
                    daemon=True
                )
                worker.start()
                self.worker_threads.append(worker)

            # Start health monitor
            self.health_monitor_thread = threading.Thread(
                target=self._health_monitor,
                name="HealthMonitor",
                daemon=True
            )
            self.health_monitor_thread.start()

            self.logger.info("✅ Production Inference System started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start inference system: {e}")
            return False

    def stop(self):
        """Stop the production inference system"""
        self.logger.info("🛑 Stopping Production Inference System")

        self.running = False
        self.camera_manager.stop()

        # Wait for workers to finish
        for worker in self.worker_threads:
            worker.join(timeout=5)

        self.logger.info("✅ Production Inference System stopped")

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)

    def _processing_worker(self):
        """Processing worker thread"""
        worker_name = threading.current_thread().name
        self.logger.info(f"Started processing worker: {worker_name}")

        while self.running:
            try:
                # Process each camera
                for cam_id in self.config.camera_urls.keys():
                    frame_data = self.camera_manager.get_latest_frame(cam_id)

                    if frame_data is None:
                        continue

                    frame, timestamp = frame_data

                    # Process frame
                    processed_frame = self._process_frame(frame, cam_id)

                    # Update data manager
                    self.data_manager.update_frame(cam_id, processed_frame)

                # Control processing rate
                time.sleep(1.0 / self.config.max_fps)

            except Exception as e:
                self.logger.error(f"Processing worker {worker_name} error: {e}")
                time.sleep(1)

    def _process_frame(self, frame: np.ndarray, cam_id: str) -> np.ndarray:
        """Process a single frame"""
        try:
            # Crowd detection
            people_count, frame = self.model_manager.process_crowd_detection(frame)
            self.data_manager.update_camera_stats(cam_id, people_count)

            # Weapon detection
            weapon_detected, frame = self.model_manager.process_weapon_detection(frame, cam_id)

            if weapon_detected:
                # Save detection image
                image_path = self.data_manager.save_detection_image(cam_id, frame)

                if image_path:
                    # Add detection record
                    detection_record = {
                        "camera": cam_id,
                        "image_path": image_path,
                        "confidence": "high"  # Could be extracted from detection
                    }
                    self.data_manager.add_detection(cam_id, detection_record)

            # Add timestamp and camera info
            cv2.putText(frame, f"Camera: {cam_id}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"People: {people_count}", (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), (10, frame.shape[0] - 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            return frame

        except Exception as e:
            self.logger.error(f"Frame processing error for {cam_id}: {e}")
            return frame

    def _health_monitor(self):
        """Health monitoring worker"""
        self.logger.info("Started health monitor")

        while self.running:
            try:
                # Collect system health metrics
                health_data = self.metrics.get_system_health()

                # Log health status
                self.logger.info(
                    f"Health: CPU={health_data['cpu_percent']:.1f}% "
                    f"Memory={health_data['memory_mb']:.0f}MB "
                    f"Threads={health_data['active_threads']}"
                )

                # Check for issues
                if health_data['memory_mb'] > self.config.max_memory_mb:
                    self.logger.warning(f"High memory usage: {health_data['memory_mb']:.0f}MB")

                if health_data['cpu_percent'] > 90:
                    self.logger.warning(f"High CPU usage: {health_data['cpu_percent']:.1f}%")

                time.sleep(self.config.health_check_interval)

            except Exception as e:
                self.logger.error(f"Health monitor error: {e}")
                time.sleep(10)

    # Flask integration methods
    def generate_video_stream(self, cam_id: str):
        """Generate video stream for Flask"""
        while True:
            frame = self.data_manager.get_latest_frame(cam_id)
            if frame is not None:
                ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
                if ret:
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')
            else:
                time.sleep(0.1)

    def get_camera_stats(self) -> dict:
        """Get camera statistics for API"""
        return self.data_manager.get_camera_stats()

    def get_detection_data(self) -> dict:
        """Get detection data for API"""
        return self.data_manager.get_detection_data()

    def get_system_status(self) -> dict:
        """Get comprehensive system status"""
        health = self.metrics.get_system_health()
        camera_stats = self.data_manager.get_camera_stats()

        return {
            "system_health": health,
            "camera_status": {
                cam_id: {
                    "active": time.time() - stats["last_update"] < 30,
                    "people_count": stats["people_count"]
                }
                for cam_id, stats in camera_stats.items()
            },
            "models_loaded": self.model_manager.crowd_model is not None and self.model_manager.weapon_model is not None,
            "running": self.running,
            "config": asdict(self.config)
        }

# Global instance for Flask integration
_inference_system: Optional[ProductionInferenceSystem] = None

def get_inference_system() -> ProductionInferenceSystem:
    """Get or create the global inference system"""
    global _inference_system
    if _inference_system is None:
        config = ProductionConfig()
        _inference_system = ProductionInferenceSystem(config)
    return _inference_system

# Flask integration functions
def start_camera_threads() -> bool:
    """Start the inference system (Flask integration)"""
    system = get_inference_system()
    return system.start()

def generate_video_stream(cam_id: str):
    """Generate video stream (Flask integration)"""
    system = get_inference_system()
    return system.generate_video_stream(cam_id)

def get_camera_stats() -> dict:
    """Get camera stats (Flask integration)"""
    system = get_inference_system()
    return system.get_camera_stats()

def get_detection_data() -> dict:
    """Get detection data (Flask integration)"""
    system = get_inference_system()
    return system.get_detection_data()

def get_tracking_data() -> dict:
    """Get tracking data (Flask integration)"""
    return {}  # Simplified for production

def get_hybrid_people_count() -> dict:
    """Get people count (Flask integration)"""
    system = get_inference_system()
    stats = system.get_camera_stats()

    return {
        cam_id: {
            "count": camera_stats["people_count"],
            "detection_count": camera_stats["people_count"],
            "tracking_count": 0,
            "method": "detection_only"
        }
        for cam_id, camera_stats in stats.items()
    }

def get_system_status() -> dict:
    """Get system status (Flask integration)"""
    system = get_inference_system()
    return system.get_system_status()
