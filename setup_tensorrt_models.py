#!/usr/bin/env python3
"""
TensorRT Model Setup Script
Converts PyTorch models to optimized TensorRT engines
"""

import os
import sys
import time
import argparse
from pathlib import Path
import torch
from ultralytics import YOL<PERSON>

def check_requirements():
    """Check if all requirements are available"""
    print("🔍 Checking requirements...")
    
    # Check CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False
    
    print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
    
    # Check TensorRT
    try:
        import tensorrt as trt
        print(f"✅ TensorRT available: {trt.__version__}")
    except ImportError:
        print("❌ TensorRT not available. Install with: pip install tensorrt")
        return False
    
    # Check Ultralytics
    try:
        from ultralytics import __version__
        print(f"✅ Ultralytics available: {__version__}")
    except ImportError:
        print("❌ Ultralytics not available. Install with: pip install ultralytics")
        return False
    
    return True

def convert_model_to_tensorrt(model_path, output_path, precision='fp16', workspace_size=4096):
    """Convert PyTorch model to TensorRT engine"""
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return False
    
    print(f"🔄 Converting {model_path} to TensorRT...")
    print(f"   Precision: {precision}")
    print(f"   Workspace: {workspace_size}MB")
    
    try:
        # Load model
        model = YOLO(model_path)
        
        # Export to TensorRT
        start_time = time.time()
        
        export_args = {
            'format': 'engine',
            'device': 0,
            'workspace': workspace_size,
            'verbose': True
        }
        
        # Set precision
        if precision == 'fp16':
            export_args['half'] = True
        elif precision == 'int8':
            export_args['int8'] = True
        
        # Export model
        exported_path = model.export(**export_args)
        
        # Move to desired location if different
        if exported_path != output_path:
            import shutil
            shutil.move(exported_path, output_path)
        
        conversion_time = time.time() - start_time
        
        # Get file size
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        
        print(f"✅ Conversion completed in {conversion_time:.1f}s")
        print(f"   Output: {output_path}")
        print(f"   Size: {file_size:.1f}MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        return False

def test_tensorrt_model(engine_path):
    """Test TensorRT engine"""
    
    if not os.path.exists(engine_path):
        print(f"❌ Engine not found: {engine_path}")
        return False
    
    print(f"🧪 Testing {engine_path}...")
    
    try:
        # Load engine
        model = YOLO(engine_path)
        
        # Create dummy input
        import numpy as np
        dummy_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # Run inference
        start_time = time.time()
        results = model(dummy_image, verbose=False)
        inference_time = time.time() - start_time
        
        print(f"✅ Test successful")
        print(f"   Inference time: {inference_time*1000:.1f}ms")
        print(f"   Detections: {len(results[0].boxes) if results[0].boxes is not None else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def benchmark_model(engine_path, num_runs=100):
    """Benchmark TensorRT engine performance"""
    
    if not os.path.exists(engine_path):
        print(f"❌ Engine not found: {engine_path}")
        return
    
    print(f"📊 Benchmarking {engine_path} ({num_runs} runs)...")
    
    try:
        # Load engine
        model = YOLO(engine_path)
        
        # Create dummy input
        import numpy as np
        dummy_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # Warmup
        for _ in range(10):
            model(dummy_image, verbose=False)
        
        # Benchmark
        times = []
        for i in range(num_runs):
            start_time = time.time()
            results = model(dummy_image, verbose=False)
            inference_time = time.time() - start_time
            times.append(inference_time * 1000)  # Convert to ms
            
            if (i + 1) % 20 == 0:
                print(f"   Progress: {i+1}/{num_runs}")
        
        # Calculate statistics
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        fps = 1000 / avg_time
        
        print(f"📈 Benchmark Results:")
        print(f"   Average: {avg_time:.1f}ms ({fps:.1f} FPS)")
        print(f"   Min: {min_time:.1f}ms")
        print(f"   Max: {max_time:.1f}ms")
        
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")

def main():
    """Main function"""
    
    parser = argparse.ArgumentParser(description='TensorRT Model Setup')
    parser.add_argument('--precision', choices=['fp32', 'fp16', 'int8'], default='fp16',
                       help='TensorRT precision mode (default: fp16)')
    parser.add_argument('--workspace', type=int, default=4096,
                       help='TensorRT workspace size in MB (default: 4096)')
    parser.add_argument('--test', action='store_true',
                       help='Test converted engines')
    parser.add_argument('--benchmark', action='store_true',
                       help='Benchmark converted engines')
    parser.add_argument('--models-dir', default='./models',
                       help='Models directory (default: ./models)')
    
    args = parser.parse_args()
    
    print("🚀 TensorRT Model Setup")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Setup paths
    models_dir = Path(args.models_dir)
    if not models_dir.exists():
        print(f"❌ Models directory not found: {models_dir}")
        sys.exit(1)
    
    # Define model configurations
    model_configs = [
        {
            'name': 'Crowd Detection',
            'pt_file': 'best_yolo11s_crowd.pt',
            'engine_file': 'best_yolo11s_crowd.engine'
        },
        {
            'name': 'Weapon Detection',
            'pt_file': 'best_yolo11x_gun.pt',
            'engine_file': 'best_yolo11x_gun.engine'
        }
    ]
    
    # Convert models
    success_count = 0
    for config in model_configs:
        print(f"\n🔧 Processing {config['name']}")
        print("-" * 30)
        
        pt_path = models_dir / config['pt_file']
        engine_path = models_dir / config['engine_file']
        
        # Check if PyTorch model exists
        if not pt_path.exists():
            print(f"⚠️  PyTorch model not found: {pt_path}")
            print("   Skipping conversion...")
            continue
        
        # Check if engine already exists
        if engine_path.exists():
            print(f"ℹ️  Engine already exists: {engine_path}")
            response = input("   Overwrite? (y/N): ").lower()
            if response != 'y':
                print("   Skipping conversion...")
                if args.test:
                    test_tensorrt_model(str(engine_path))
                if args.benchmark:
                    benchmark_model(str(engine_path))
                continue
        
        # Convert model
        if convert_model_to_tensorrt(
            str(pt_path),
            str(engine_path),
            args.precision,
            args.workspace
        ):
            success_count += 1
            
            # Test if requested
            if args.test:
                test_tensorrt_model(str(engine_path))
            
            # Benchmark if requested
            if args.benchmark:
                benchmark_model(str(engine_path))
        
        print()
    
    # Summary
    print("📋 Conversion Summary")
    print("=" * 50)
    print(f"✅ Successfully converted: {success_count}/{len(model_configs)} models")
    print(f"🔧 Precision: {args.precision}")
    print(f"💾 Workspace: {args.workspace}MB")
    
    if success_count == len(model_configs):
        print("\n🎉 All models converted successfully!")
        print("\nNext steps:")
        print("1. Update your .env file with USE_TENSORRT=true")
        print("2. Run: python run_production.py --check-only")
        print("3. Start inference: python run_production.py")
    else:
        print(f"\n⚠️  {len(model_configs) - success_count} models failed to convert")
        print("Check the error messages above and ensure:")
        print("- PyTorch models exist in ./models/")
        print("- CUDA and TensorRT are properly installed")
        print("- Sufficient GPU memory is available")

if __name__ == "__main__":
    main()
