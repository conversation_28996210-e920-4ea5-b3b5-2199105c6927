# [Flask Dashboard Material](https://appseed.us/product/material-dashboard/flask/)

Open-source **[Flask Dashboard](https://appseed.us/admin-dashboards/flask/)** generated by `AppSeed` op top of a modern design. Designed for those who like bold elements and beautiful websites, **Material Dashboard** is ready to help you create stunning websites and web apps. **Material Dashboard** is built with over 70 frontend individual elements, like buttons, inputs, navbars, nav tabs, cards, or alerts, giving you the freedom of choosing and combining.

- 👉 [Flask Dashboard Material](https://appseed.us/product/material-dashboard/flask/) - product page
- 👉 [Flask Dashboard Material](https://flask-material-dashboard.appseed-srv1.com/) - LIVE deployment 

<br />

## Features

> `Have questions?` Contact **[Support](https://appseed.us/support/)** (Email & Discord) provided by **AppSeed**

| Free Version                          | [PRO Version](https://appseed.us/product/material-dashboard2-pro/flask/)          | [Custom Development](https://appseed.us/custom-development/) |  
| --------------------------------------| --------------------------------------| --------------------------------------|
| ✓ **Up-to-date dependencies**             | **Everything in Free**, plus:                                        | **Everything in PRO**, plus:          |
| ✓ Best Practices                          | ✅ **Premium Bootstrap 5 Design**                                    | ✅ **1 Week** `Custom Development`    |  
| ✓ DB: SQLite, MySql                       | ✅ **PRO Support** - [Email & Discord](https://appseed.us/support/)  | ✅ **Team**: PM, Developer, Tester   |
| ✓ DB Tools: ORM, Flask-Migrate            | ✅ `Private REPO Access`                                             | ✅ Weekly Sprints                    |
| ✓ Session-Based authentication            |  -                                                                    | ✅ Technical SPECS                   |
| ✓ `Docker`                                |  -                                                                    | ✅ Documentation                     |
| ✓ `CI/CD` Flow via Render                 |  -                                                                    | ✅ **30 days Delivery Warranty**     |
| ✓ `Free Support`                          |  -                                                                    | ✅ [CI/CD for AWS, DO](https://appseed.us/terms/#section-ci-cd) **(Extra)**    |
| ---------------------------------         | ---------------------------------                                     | ---------------------------------  |
| ✓ [LIVE Demo](https://flask-material-dashboard.appseed-srv1.com/)  | 🚀 [LIVE Demo](https://flask-material-dashboard2-pro.appseed-srv1.com/) `PRO`  | **[Get in Touch ➡️](https://appseed.us/custom-development/)** |  

![Material Dashboard - Full-Stack Starter generated by AppSeed.](https://user-images.githubusercontent.com/51070104/169301658-6cf27993-c451-4cd4-9ffa-2968b8981167.png)

<br /> 

## ✅ Start in `Docker`

> 👉 **Step 1** - Download the code from the GH repository (using `GIT`) 

```bash
$ git clone https://github.com/FalconElectronics/Integrated-weapon-and-crowd.git
$ cd Integrated-weapon-and-crowd
```

<br /> 

> 👉 **Step 2** - Start the APP in `Docker`

```bash
$ docker-compose up --build 
```

Visit `http://localhost:5085` in your browser. The app should be up & running.

<br /> 

## ✅ Manual Build 

> Download the code 

```bash
$ git clone https://github.com/FalconElectronics/Integrated-weapon-and-crowd.git
$ cd Integrated-weapon-and-crowd
```

<br />

### 👉 Set Up for `Unix`, `MacOS` 

> Install modules via `VENV`  

```bash
$ virtualenv env
$ source env/bin/activate
$ pip3 install -r requirements.txt
```
> Install torch with cuda
```bash
pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
```
<br />

> Download Bytetrack

```bash
git clone https://github.com/ifzhang/ByteTrack.git
cd ByteTrack
```
> Delete the following libraries from requirements.txt
```bash
# verified versions
onnx==1.8.1
onnxruntime==1.8.0
onnx-simplifier==0.3.5
```
> Install requirements.txt

```bash
pip3 install -r requirements.txt
python3 setup.py develop
cd ..
```
<br />
> Set Up Flask Environment

```bash
$ export FLASK_APP=run.py
$ export FLASK_ENV=development
```

<br />

> Start the app

```bash
$ flask run
```

At this point, the app runs at `http://127.0.0.1:5000/`. 

<br />

### 👉 Set Up for `Windows` 

> Install modules via `VENV` (windows) 

```
$ virtualenv env
$ .\env\Scripts\activate
$ pip3 install -r requirements.txt
```
> Install torch with cuda
```bash
pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
```
<br />

> Download Bytetrack

```bash
git clone https://github.com/ifzhang/ByteTrack.git
cd ByteTrack
```
> Delete the following libraries from requirements.txt
```bash
# verified versions
onnx==1.8.1
onnxruntime==1.8.0
onnx-simplifier==0.3.5
```
> Install requirements.txt

```bash
pip3 install -r requirements.txt
python3 setup.py develop
cd ..
```
> Configure the Application
Edit the `inference.py` file to:
1. Update the ByteTrack path to match your environment
2. Add your Hugging Face token for authentication
3. Check RTSP links
   
> Set Up Flask Environment

```bash
$ # CMD 
$ set FLASK_APP=run.py
$ set FLASK_ENV=development
$
$ # Powershell
$ $env:FLASK_APP = ".\run.py"
$ $env:FLASK_ENV = "development"
```

<br />

> Start the app

```bash
$ flask run
```

At this point, the app runs at `http://127.0.0.1:5000/`. 

<br />

### 👉 Create Users

By default, the app redirects guest users to authenticate. In order to access the private pages, follow this set up: 

- Start the app via `flask run`
- Access the `registration` page and create a new user:
  - `http://127.0.0.1:5000/register`
- Access the `sign in` page and authenticate
  - `http://127.0.0.1:5000/login`

<br />

## ✅ Codebase

The project is coded using blueprints, app factory pattern, dual configuration profile (development and production) and an intuitive structure presented bellow:

```bash
< PROJECT ROOT >
   |
   |-- apps/
   |    |
   |    |-- home/                           # A simple app that serve HTML files
   |    |    |-- routes.py                  # Define app routes
   |    |
   |    |-- authentication/                 # Handles auth routes (login and register)
   |    |    |-- routes.py                  # Define authentication routes  
   |    |    |-- models.py                  # Defines models  
   |    |    |-- forms.py                   # Define auth forms (login and register) 
   |    |
   |    |-- static/
   |    |    |-- <css, JS, images>          # CSS files, Javascripts files
   |    |
   |    |-- templates/                      # Templates used to render pages
   |    |    |-- includes/                  # HTML chunks and components
   |    |    |    |-- navigation.html       # Top menu component
   |    |    |    |-- sidebar.html          # Sidebar component
   |    |    |    |-- footer.html           # App Footer
   |    |    |    |-- scripts.html          # Scripts common to all pages
   |    |    |
   |    |    |-- layouts/                   # Master pages
   |    |    |    |-- base-fullscreen.html  # Used by Authentication pages
   |    |    |    |-- base.html             # Used by common pages
   |    |    |
   |    |    |-- accounts/                  # Authentication pages
   |    |    |    |-- login.html            # Login page
   |    |    |    |-- register.html         # Register page
   |    |    |
   |    |    |-- home/                      # UI Kit Pages
   |    |         |-- index.html            # Index page
   |    |         |-- 404-page.html         # 404 page
   |    |         |-- *.html                # All other pages
   |    |    
   |  config.py                             # Set up the app
   |    __init__.py                         # Initialize the app
   |
   |-- requirements.txt                     # App Dependencies
   |
   |-- .env                                 # Inject Configuration via Environment
   |-- run.py                               # Start the app - WSGI gateway
   |
   |-- ************************************************************************
```

<br />

## ✅ [PRO Version](https://appseed.us/product/material-dashboard2-pro/flask/)

> For more components, pages, and priority on support, feel free to take a look at this amazing starter:

**Flask** starter styled with **[Material Dashboard PRO](https://appseed.us/product/material-dashboard2-pro/flask/)**, a premium `Bootstrap 5` KIT from `Creative-Tim`.
The product is designed to deliver the best possible user experience with highly customizable feature-rich pages. 

- 👉 [Flask Material 2 PRO](https://appseed.us/product/material-dashboard2-pro/flask/) - Product page
- 👉 [Flask Material 2 PRO](https://flask-material-dashboard2-pro.appseed-srv1.com) - LIVE Demo

![Flask Material Dash 2 PRO - Premium starter crafted by AppSeed and Creative-Tim.](https://user-images.githubusercontent.com/51070104/218248092-adf0dfe6-10bb-4665-aa89-dd6265d11995.png)


---
[Flask Dashboard Material](https://appseed.us/product/material-dashboard/flask/) - Free starter generated by **[App Generator](https://appseed.us/generator/)**.
