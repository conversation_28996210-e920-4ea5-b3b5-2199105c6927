# TensorRT Inference Configuration
# Simple configuration for direct TensorRT model usage

# Primary TensorRT Model Paths
CROWD_TENSORRT_MODEL=./models/best_yolo11s_crowd.engine
WEAPON_TENSORRT_MODEL=./models/best_yolo11x_gun.engine

# Fallback PyTorch Model Paths
CROWD_PT_MODEL=./models/best_yolo11s_crowd.pt
WEAPON_PT_MODEL=./models/best_yolo11x_gun.pt

# Camera URLs
CAM1_URL=rtsp://virtualpresenz.duckdns.org:8554/stream0
CAM2_URL=rtsp://virtualpresenz2.duckdns.org:8553/stream0
CAM3_URL=rtsp://virtualpresenz1.duckdns.org:8555/stream0

# Detection Confidence Thresholds
CROWD_CONFIDENCE=0.8
WEAPON_CONFIDENCE=0.8

# Output Directories
OUTPUT_DIR=apps/static/detection
DETECTION_DIR=apps/static/assets/detection
