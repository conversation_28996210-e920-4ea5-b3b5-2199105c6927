#!/bin/bash

# Lambda.ai Post-Setup Helper Script
# Handles Lambda.ai specific configuration after main setup_environment.sh runs

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Configuration
PROJECT_DIR="${PROJECT_DIR:-$HOME/automated-setup/Integrated-weapon-and-crowd}"
HUGGINGFACE_TOKEN="${HUGGINGFACE_TOKEN:-}"

create_lambda_config() {
    log_info "Creating Lambda.ai specific configuration..."
    
    cd "$PROJECT_DIR"
    
    # Create .env file for Flask
    cat > .env << 'EOF'
# Lambda.ai Development Environment
DEBUG=True
FLASK_APP=run.py
FLASK_DEBUG=1
SECRET_KEY=lambda-ai-dev-secret-$(date +%s)
FLASK_ENV=development
EOF
    
    # Update config.ini with actual paths
    if [ -f "apps/home/<USER>" ]; then
        # Update existing config.ini
        sed -i "s|<bytetrack_path>|$PROJECT_DIR/ByteTrack|g" apps/home/<USER>
        if [ -n "$HUGGINGFACE_TOKEN" ]; then
            sed -i "s|<huggingface_token>|$HUGGINGFACE_TOKEN|g" apps/home/<USER>
        fi
    else
        # Create new config.ini
        mkdir -p apps/home
        cat > apps/home/<USER>
[paths]
bytetrack_path = $PROJECT_DIR/ByteTrack

[huggingface]
token = $HUGGINGFACE_TOKEN
EOF
    fi
    
    log_success "Configuration files created"
}

create_utility_scripts() {
    log_info "Creating Lambda.ai utility scripts..."
    
    cd "$PROJECT_DIR"
    
    # Start application script
    cat > start_app.sh << 'EOF'
#!/bin/bash
cd ~/automated-setup/Integrated-weapon-and-crowd
source env/bin/activate

echo "🚀 Starting Flask application on Lambda.ai..."
echo "   External access: http://$(curl -s ifconfig.me):5000"
echo "   Local access: http://localhost:5000"
echo "   Press Ctrl+C to stop"
echo ""

export FLASK_APP=run.py
export FLASK_ENV=development
flask run --host=0.0.0.0 --port=5000
EOF
    chmod +x start_app.sh
    
    # Status check script
    cat > check_status.sh << 'EOF'
#!/bin/bash
cd ~/automated-setup/Integrated-weapon-and-crowd
source env/bin/activate

echo "=== Lambda.ai Instance Status ==="
echo "Date: $(date)"
echo "Uptime: $(uptime)"
echo ""

echo "=== GPU Status ==="
nvidia-smi --query-gpu=name,memory.total,memory.used,temperature.gpu --format=csv,noheader,nounits 2>/dev/null || echo "No GPU detected"
echo ""

echo "=== Python Environment ==="
echo "Python: $(python --version)"
echo "Virtual env: ${VIRTUAL_ENV:-'Not activated'}"
echo "PyTorch: $(python -c 'import torch; print(torch.__version__)' 2>/dev/null || echo 'Not installed')"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())' 2>/dev/null || echo 'Unknown')"
echo ""

echo "=== Flask Application ==="
echo "Flask app: ${FLASK_APP:-'Not set'}"
echo "Flask env: ${FLASK_ENV:-'Not set'}"
echo ""

echo "=== Configuration ==="
echo "Config file: $(ls apps/home/<USER>/dev/null && echo 'Found' || echo 'Missing')"
echo "ByteTrack: $(ls ByteTrack/ 2>/dev/null >/dev/null && echo 'Installed' || echo 'Missing')"
echo ".env file: $(ls .env 2>/dev/null && echo 'Found' || echo 'Missing')"
echo ""

echo "=== Disk Usage ==="
echo "Project directory: $(du -sh . 2>/dev/null || echo 'Unknown')"
echo "Root filesystem: $(df -h / | tail -1)"
EOF
    chmod +x check_status.sh
    
    # Test models script
    cat > test_models.sh << 'EOF'
#!/bin/bash
cd ~/automated-setup/Integrated-weapon-and-crowd
source env/bin/activate

echo "🤖 Testing AI models setup..."

python -c "
import sys
sys.path.append('.')

try:
    from huggingface_hub import login
    with open('apps/home/<USER>', 'r') as f:
        content = f.read()
        if '<huggingface_token>' in content:
            print('⚠️  Hugging Face token not configured yet')
            print('   Update apps/home/<USER>')
        else:
            print('✅ Hugging Face token appears to be configured')
except Exception as e:
    print(f'❌ Configuration check failed: {e}')

try:
    import torch
    print(f'✅ PyTorch version: {torch.__version__}')
    if torch.cuda.is_available():
        print(f'✅ GPU available: {torch.cuda.get_device_name(0)}')
        print(f'   Memory: {torch.cuda.get_device_properties(0).total_memory // 1024**3} GB')
    else:
        print('⚠️  No GPU available, will use CPU')
except Exception as e:
    print(f'❌ PyTorch check failed: {e}')

print('')
print('📝 Notes:')
print('   - Models will download automatically on first app run')
print('   - Ensure Hugging Face token is configured in config.ini')
print('   - Use ./start_app.sh to start the application')
"
EOF
    chmod +x test_models.sh
    
    log_success "Utility scripts created"
}

validate_setup() {
    log_info "Validating Lambda.ai setup..."
    
    cd "$PROJECT_DIR"
    
    # Check if we're in the right directory
    if [ ! -f "run.py" ]; then
        log_warning "run.py not found - are we in the right directory?"
        return 1
    fi
    
    # Check virtual environment
    if [ ! -d "env" ]; then
        log_warning "Virtual environment not found"
        return 1
    fi
    
    # Check ByteTrack
    if [ ! -d "ByteTrack" ]; then
        log_warning "ByteTrack directory not found"
        return 1
    fi
    
    # Check configuration
    if [ ! -f "apps/home/<USER>" ]; then
        log_warning "Configuration file not found"
        return 1
    fi
    
    log_success "Basic validation passed"
}

create_setup_summary() {
    log_info "Creating setup summary..."
    
    cd "$PROJECT_DIR"
    
    cat > LAMBDA_SETUP_SUMMARY.md << EOF
# Lambda.ai Setup Summary

**Setup completed:** $(date)
**Project directory:** $PROJECT_DIR
**Setup method:** Using existing setup_environment.sh + Lambda.ai post-setup

## 🚀 Quick Start
\`\`\`bash
cd ~/automated-setup/Integrated-weapon-and-crowd
./start_app.sh
\`\`\`

## 📊 Status Check
\`\`\`bash
./check_status.sh
\`\`\`

## 🤖 Test Models
\`\`\`bash
./test_models.sh
\`\`\`

## 🌐 Access URLs
- External: http://\$(curl -s ifconfig.me):5000
- Local: http://localhost:5000

## 📁 Key Files
- \`start_app.sh\` - Start the Flask application
- \`check_status.sh\` - Check system and environment status
- \`test_models.sh\` - Test AI models and GPU setup
- \`apps/home/<USER>
- \`.env\` - Flask environment variables

## 🔧 Manual Commands
\`\`\`bash
# Activate environment
source env/bin/activate

# Start Flask app manually
export FLASK_APP=run.py
flask run --host=0.0.0.0 --port=5000
\`\`\`

## 📝 Notes
- Setup leveraged your existing setup_environment.sh script
- Models download automatically on first app run
- GPU status: Run \`nvidia-smi\` to check
- Lambda.ai instance remains running after setup
EOF
    
    log_success "Setup summary created"
}

main() {
    echo "🔧 Lambda.ai Post-Setup Configuration"
    echo "====================================="
    
    # Validate we have the project directory
    if [ ! -d "$PROJECT_DIR" ]; then
        echo "❌ Project directory not found: $PROJECT_DIR"
        echo "   Make sure setup_environment.sh ran successfully"
        exit 1
    fi
    
    create_lambda_config
    create_utility_scripts
    validate_setup
    create_setup_summary
    
    echo ""
    log_success "🎉 Lambda.ai post-setup completed!"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Update Hugging Face token in apps/home/<USER>"
    echo "   2. Test setup: ./test_models.sh"
    echo "   3. Start application: ./start_app.sh"
    echo "   4. Check status: ./check_status.sh"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi