#!/usr/bin/env python3
"""
Production Inference System Runner
Optimized for hosting and deployment
"""

import os
import sys
import time
import argparse
from pathlib import Path

def setup_production_environment():
    """Setup production environment and paths"""
    
    # Load environment variables from .env file
    try:
        from dotenv import load_dotenv
        if os.path.exists('.env'):
            load_dotenv('.env')
            print("✅ Loaded .env file")
        else:
            print("⚠️  No .env file found")
    except ImportError:
        print("⚠️  python-dotenv not installed, install with: pip install python-dotenv")
    
    # Ensure we're in the right directory
    if not os.path.exists('apps'):
        print("❌ Error: Must run from project root directory")
        sys.exit(1)
    
    # Add current directory to Python path
    if '.' not in sys.path:
        sys.path.insert(0, '.')
    
    # Set production environment
    os.environ.setdefault('FLASK_ENV', 'production')
    os.environ.setdefault('PYTHONPATH', '.')
    
    print("✅ Production environment configured")

def check_models():
    """Check if required model files exist or can be downloaded"""
    use_cloud_models = os.getenv('USE_CLOUD_MODELS', 'false').lower() == 'true'
    
    if use_cloud_models:
        print("🌐 Cloud models enabled - checking configuration...")
        
        # Check required environment variables
        hf_token = os.getenv('HUGGINGFACE_TOKEN')
        crowd_repo = os.getenv('HF_CROWD_REPO')
        weapon_repo = os.getenv('HF_WEAPON_REPO')
        
        if not hf_token:
            print("❌ HUGGINGFACE_TOKEN not set")
            return False
        
        if not crowd_repo or not weapon_repo:
            print("❌ HF_CROWD_REPO and HF_WEAPON_REPO must be set")
            print(f"   Current: crowd_repo={crowd_repo}, weapon_repo={weapon_repo}")
            return False
        
        # Try to download models to verify access
        try:
            from apps.home.cloud_model_manager import download_production_models
            print("🔍 Testing model download...")
            model_paths = download_production_models()
            
            if len(model_paths) == 2:
                print("✅ Cloud models accessible and downloaded")
                print(f"   - Crowd model: {model_paths.get('crowd_model', 'Not found')}")
                print(f"   - Weapon model: {model_paths.get('weapon_model', 'Not found')}")
                return True
            else:
                print(f"❌ Only {len(model_paths)}/2 models downloaded")
                return False
                
        except Exception as e:
            print(f"❌ Failed to test cloud model download: {e}")
            return False
    
    else:
        # Check local models
        print("📁 Checking local model files...")
        models_dir = Path('./models')
        
        required_models = [
            'best_yolo11s_crowd.engine',
            'best_yolo11x_gun.engine'
        ]
        
        missing_models = []
        for model in required_models:
            model_path = models_dir / model
            if not model_path.exists():
                # Check for .pt fallback
                pt_model = model_path.with_suffix('.pt')
                if not pt_model.exists():
                    missing_models.append(model)
        
        if missing_models:
            print("❌ Missing model files:")
            for model in missing_models:
                print(f"   - {model}")
            print("\nOptions:")
            print("1. Place model files in ./models/ directory")
            print("2. Enable cloud models: set USE_CLOUD_MODELS=true in .env")
            return False
        
        print("✅ Local model files found")
        return True

def check_configuration():
    """Check production configuration"""
    try:
        from apps.home.inference_production import ProductionConfig
        config = ProductionConfig()
        print("✅ Configuration validated")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def run_standalone():
    """Run standalone inference system"""
    try:
        from apps.home.inference_production import ProductionInferenceSystem, ProductionConfig, setup_production_logging
        
        print("🚀 Starting Production Inference System")
        print("=" * 40)
        
        # Setup configuration and logging
        config = ProductionConfig()
        logger = setup_production_logging(config)
        
        # Create and start system
        system = ProductionInferenceSystem(config)
        
        if system.start():
            print("✅ System started successfully")
            print("\n📊 System Status:")
            print("- Models: Loaded")
            print("- Cameras: Connecting...")
            print("- Processing: Active")
            print("\n🔗 Access points:")
            print("- System will process camera feeds automatically")
            print("- Check logs for detailed status")
            
            try:
                while True:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Shutdown requested")
            finally:
                system.stop()
        else:
            print("❌ Failed to start system")
            return False
            
    except Exception as e:
        print(f"❌ Standalone mode error: {e}")
        return False
    
    return True

def run_flask():
    """Run Flask web application with production inference system"""
    try:
        print("🌐 Starting Flask Web Application")
        print("=" * 40)

        # Import Flask app creation
        try:
            from apps import create_app
            from apps.config import ProductionConfig

            # Create app with production config
            app = create_app(ProductionConfig)
            print("✅ Flask app created with ProductionConfig")

        except ImportError as e:
            print(f"⚠️  Could not import Flask app: {e}")
            # Fallback: create a simple Flask app
            from flask import Flask, jsonify
            app = Flask(__name__)

            # Add basic routes
            @app.route('/')
            def index():
                return "Inference System Running"

            @app.route('/api/health')
            def health():
                return jsonify({"status": "healthy", "timestamp": time.time()})

            print("✅ Created fallback Flask app")

        # CRITICAL FIX: Use production inference system instead of old inference.py
        print("🔧 Initializing production inference system...")
        try:
            # Import the production inference system
            from apps.home.inference_production import ProductionInferenceSystem, ProductionConfig
            
            # Create and start the production inference system
            config = ProductionConfig()
            inference_system = ProductionInferenceSystem(config)
            
            if inference_system.start():
                print("✅ Production inference system started")
                
                # Make the system available globally for Flask routes
                app.config['INFERENCE_SYSTEM'] = inference_system
                
                # Add production routes that use the inference system
                @app.route('/api/system-status')
                def system_status():
                    system = app.config['INFERENCE_SYSTEM']
                    return jsonify(system.get_system_status())
                
                @app.route('/api/camera-stats')
                def camera_stats():
                    system = app.config['INFERENCE_SYSTEM']
                    return jsonify(system.get_camera_stats())
                
                @app.route('/api/detection-data')
                def detection_data():
                    system = app.config['INFERENCE_SYSTEM']
                    return jsonify(system.get_detection_data())
                
                # Video streaming routes
                @app.route('/video_feed/<cam_id>')
                def video_feed(cam_id):
                    from flask import Response
                    if cam_id not in ["cam1", "cam2", "cam3"]:
                        return "Invalid camera ID", 404
                    
                    system = app.config['INFERENCE_SYSTEM']
                    return Response(
                        system.generate_video_stream(cam_id),
                        mimetype='multipart/x-mixed-replace; boundary=frame'
                    )
                
            else:
                print("❌ Failed to start production inference system")
                return False
                
        except ImportError as e:
            print(f"⚠️  Production inference system not available: {e}")
            print("🔄 Falling back to basic inference system...")
            
            # Fallback to a simplified system without the old inference.py conversion
            try:
                # Import just the model loading parts without conversion
                from apps.home.cloud_model_manager import download_production_models
                
                print("📤 Downloading TensorRT engines...")
                model_paths = download_production_models()
                
                if len(model_paths) == 2:
                    print("✅ TensorRT engines downloaded successfully")
                    
                    # Create basic routes for the old system compatibility
                    @app.route('/json/tracking')
                    def tracking_json():
                        return jsonify({})
                    
                    @app.route('/json/detection')
                    def detection_json():
                        return jsonify({})
                    
                    @app.route('/json/camera_stats')
                    def camera_stats_json():
                        return jsonify({
                            "cam1": {"people_count": 0, "entries": 0, "exits": 0},
                            "cam2": {"people_count": 0, "entries": 0, "exits": 0},
                            "cam3": {"people_count": 0, "entries": 0, "exits": 0}
                        })
                    
                    print("✅ Basic compatibility routes added")
                else:
                    print(f"❌ Only {len(model_paths)}/2 models downloaded")
                    
            except Exception as fallback_error:
                print(f"❌ Fallback setup failed: {fallback_error}")
                return False

        # Run Flask app
        host = os.getenv('FLASK_HOST', '0.0.0.0')
        port = int(os.getenv('FLASK_PORT', '5000'))
        debug = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'

        print(f"🚀 Starting Flask server on {host}:{port}")
        print(f"🔗 Access your application at: http://localhost:{port}")
        print(f"🔗 Health check: http://localhost:{port}/api/health")

        # Graceful shutdown handling
        def cleanup():
            if 'INFERENCE_SYSTEM' in app.config:
                print("🛑 Shutting down inference system...")
                app.config['INFERENCE_SYSTEM'].stop()

        import atexit
        atexit.register(cleanup)

        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )

    except Exception as e:
        print(f"❌ Flask mode error: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Production Inference System')
    parser.add_argument('--mode', choices=['standalone', 'flask'], default='flask',
                       help='Run mode: standalone or flask (default: flask)')
    parser.add_argument('--check-only', action='store_true',
                       help='Only check configuration and models, do not start system')
    
    args = parser.parse_args()
    
    print("🔍 Production Inference System")
    print("=" * 60)
    
    # Setup environment
    setup_production_environment()
    
    # Check models
    if not check_models():
        sys.exit(1)
    
    # Check configuration
    if not check_configuration():
        sys.exit(1)
    
    if args.check_only:
        print("✅ All checks passed!")
        return
    
    # Run system
    if args.mode == 'standalone':
        success = run_standalone()
    elif args.mode == 'flask':
        success = run_flask()
    else:
        print(f"❌ Unknown mode: {args.mode}")
        success = False
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()