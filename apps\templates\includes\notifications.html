{% block notifications %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification System</title>

    <!-- Bootstrap 5 CSS -->
    <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"> -->

    <style>
    #notification-container {
        z-index: 1050;
        width: 350px;
        top: 20px;
        right: 20px;
    }

    .notification-alert {
        transition: opacity 0.3s ease-in-out;
        background-color: rgba(200, 255, 200, 0.9); /* Soft light color */
        border-radius: 8px; /* Rounded corners for a softer look */
        padding: 15px;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1); /* Softer shadow */
        color: #333; /* Dark text for contrast */
    }
</style>
</head>
<body>

<!-- Notification Container -->
<div id="notification-container" class="position-fixed"></div>

<!-- Bootstrap 5 JS Bundle with <PERSON><PERSON> -->
<!-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script> -->

<script>
// Notification Configuration
const notificationConfig = {
    types: {
        info: { class: 'alert-info', icon: 'ℹ️' },
        success: { class: 'alert-success', icon: '✅' },
        warning: { class: 'alert-warning', icon: '⚠️' },
        error: { class: 'alert-danger', icon: '❌' }
    },
    defaultDuration: 5000,
    maxNotifications: 3
};

function showNotification(message, options = {}) {
    const config = {
        type: 'info',
        duration: notificationConfig.defaultDuration,
        ...options
    };

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert notification-alert alert-dismissible fade show ${notificationConfig.types[config.type].class}`;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <span class="me-2">${notificationConfig.types[config.type].icon}</span>
            <div>${message}</div>
            <button type="button"
                    class="btn-close ms-auto"
                    data-bs-dismiss="alert"
                    aria-label="Close"></button>
        </div>
    `;

    // Add to container
    const container = document.getElementById('notification-container');
    container.prepend(notification);

    // Limit maximum notifications
    while (container.children.length > notificationConfig.maxNotifications) {
        container.lastChild.remove();
    }

    // Auto-dismiss after duration
    if (config.duration > 0) {
        setTimeout(() => {
            const bsAlert = bootstrap.Alert.getOrCreateInstance(notification);
            bsAlert.close();
        }, config.duration);
    }
}

// Example Usage:
// showNotification('File uploaded successfully', { type: 'success', duration: 3000 });
showNotification('Alert! There is a HandGun Detected, Click here  to go to detailed View' , { type: 'error', duration: 5000 });
</script>

</body>
</html>

{% endblock notifications %}
