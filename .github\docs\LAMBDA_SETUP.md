# Lambda.ai Automated Setup Guide

This guide explains how to use GitHub Actions to automatically set up your development environment on Lambda.ai GPU instances.

## 🎯 Overview

The automated setup leverages your existing `setup_environment.sh` script plus Lambda.ai specific configurations to create a complete development environment remotely.

### Setup Flow:
1. **GitHub Actions** → SSH into Lambda.ai instance
2. **Install** system dependencies 
3. **Run** existing `setup_environment.sh` script
4. **Apply** Lambda.ai specific configuration
5. **Create** utility scripts for easy management
6. **Validate** setup and keep instance running

## 🔧 Prerequisites

### 1. Repository Secrets
Set these secrets in your GitHub repository (`Settings > Secrets and variables > Actions`):

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `LAMBDA_SSH_PRIVATE_KEY` | Private SSH key for Lambda.ai instance | `-----BEGIN OPENSSH PRIVATE KEY-----\n...` |
| `GIT_SSH_PRIVATE_KEY` | SSH deploy key for private repo access | Base64 encoded private key |
| `HUGGINGFACE_TOKEN` | Your Hugging Face access token | `hf_xxxxxxxxxxxx` |

### 2. Lambda.ai Instance Setup
- Launch a Lambda.ai GPU instance
- Ensure SSH access is enabled
- Note the instance IP address
- Verify GPU drivers are installed (`nvidia-smi`)

## 🚀 Usage

### Manual Trigger
1. Go to **Actions** tab in your GitHub repository
2. Select **"Setup Development Environment on Lambda.ai"**
3. Click **"Run workflow"**
4. Fill in the parameters:
   - **Lambda.ai IP**: Your instance IP address
   - **SSH Username**: Usually `ubuntu` 
   - **Setup Type**: `development`, `production`, or `testing`
   - **Skip Model Download**: Check for faster setup testing

### Workflow Parameters

| Parameter | Required | Default | Description |
|-----------|----------|---------|-------------|
| `lambda_ip` | ✅ | - | Lambda.ai instance IP address |
| `lambda_user` | ❌ | `ubuntu` | SSH username for the instance |
| `setup_type` | ✅ | `development` | Environment type |
| `skip_model_download` | ❌ | `false` | Skip model download for testing |

## 📁 What Gets Installed

### System Dependencies
- Python 3 + pip + venv
- Build tools (gcc, make, etc.)
- Git, curl, wget
- FFmpeg and OpenCV dependencies
- htop, tmux for monitoring

### Python Environment
- Virtual environment in `~/automated-setup/Integrated-weapon-and-crowd/env/`
- PyTorch with CUDA 12.4 support
- All project dependencies from `requirements.txt`
- ByteTrack with modified requirements

### Configuration Files
- `apps/home/<USER>
- `.env` - Flask environment variables
- Utility scripts for easy management

### Utility Scripts Created
- `start_app.sh` - Start the Flask application
- `check_status.sh` - Check system status and health
- `test_models.sh` - Test AI models and GPU setup

## 🎮 Post-Setup Usage

After successful setup, SSH into your Lambda.ai instance:

```bash
ssh ubuntu@YOUR_LAMBDA_IP
cd ~/automated-setup/Integrated-weapon-and-crowd
```

### Quick Start
```bash
# Start the application
./start_app.sh

# Check system status
./check_status.sh

# Test models and GPU
./test_models.sh
```

### Manual Commands
```bash
# Activate environment
source env/bin/activate

# Start Flask manually
export FLASK_APP=run.py
flask run --host=0.0.0.0 --port=5000

# Check GPU status
nvidia-smi
```

### Access URLs
- **External**: `http://YOUR_LAMBDA_IP:5000`
- **Local**: `http://localhost:5000`

## 🔍 Troubleshooting

### Common Issues

**SSH Connection Failed**
- Verify Lambda.ai instance is running
- Check IP address is correct
- Ensure SSH private key is properly formatted in secrets

**Setup Script Failed**
- Check that `GIT_SSH_PRIVATE_KEY` is properly base64 encoded
- Verify deploy key has access to your private repository
- Review workflow logs for specific error messages

**Models Not Downloading**
- Verify `HUGGINGFACE_TOKEN` is valid
- Check token has access to the required model repositories
- Look at `apps/home/<USER>

**GPU Not Detected**
- Run `nvidia-smi` to check GPU availability
- Verify Lambda.ai instance has GPU enabled
- Check CUDA installation with PyTorch test

### Debugging Steps

1. **Check Workflow Logs**
   - Go to Actions tab and view the failed run
   - Look for specific error messages

2. **SSH to Instance Manually**
   ```bash
   ssh ubuntu@YOUR_LAMBDA_IP
   cd ~/automated-setup/Integrated-weapon-and-crowd
   ./check_status.sh
   ```

3. **Verify Setup**
   ```bash
   # Check if main setup completed
   ls ~/automated-setup/Integrated-weapon-and-crowd/
   
   # Check virtual environment
   source env/bin/activate
   python --version
   pip list | grep torch
   ```

4. **Re-run Post-Setup Manually**
   ```bash
   # If only post-setup failed, you can run it manually
   cd ~/automated-setup/Integrated-weapon-and-crowd
   # Update config.ini manually if needed
   ```

## 📝 Notes

- **Instance Lifecycle**: Lambda.ai instance remains running after setup
- **Cost Management**: Remember to stop instances when not in use
- **Model Caching**: Models are cached after first download
- **Setup Time**: Initial setup takes ~10-15 minutes
- **Re-running**: Safe to re-run workflow on same instance

## 🔄 Updates and Maintenance

### Updating the Setup
- Changes to `setup_environment.sh` are automatically used
- Workflow updates require repository updates
- Helper scripts can be updated independently

### Manual Cleanup
```bash
# Remove automated setup
rm -rf ~/automated-setup

# Clean up any remaining processes
pkill -f flask
```

## 🤝 Support

For issues with:
- **Lambda.ai platform**: Contact Lambda.ai support
- **GitHub Actions**: Check repository permissions and secrets
- **Application setup**: Review logs and troubleshooting section above