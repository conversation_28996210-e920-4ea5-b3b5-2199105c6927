# Publicly accessing RTSP Stream 

Here’s a step-by-step for exposing your RTSP camera via your home router and a free DuckDNS hostname:

---

## 1. Give Your Windows Camera Host a Fixed LAN IP

You want your port-forward rule to always point at the same internal address.

1. **Via DHCP reservation (preferred):**

   * Log into your router’s admin UI (often at [http://***********](http://***********) or [http://***********](http://***********)).
   * Find **DHCP** or **Address Reservation**, locate your Windows machine’s MAC address, and reserve an IP (e.g. `************`).

2. **Or set static on Windows:**

   * Open **Settings → Network & Internet → Change adapter options**.
   * Right-click your active adapter → **Properties** → select **Internet Protocol Version 4 (TCP/IPv4)** → **Properties**.
   * Choose **Use the following IP address** and enter:

     ```
     IP address:    ************
     Subnet mask:   *************
     Default gateway: ***********  (your router’s IP)
     DNS:           *******  (or your ISP’s)
     ```

---

## 2. Configure Port-Forwarding on Your Router

1. In your router’s admin UI, find **Port Forwarding**, **NAT**, or **Virtual Servers**.
2. **Add a new rule**:

   * **Service name**: “RTSP Camera”
   * **Protocol**: TCP (some routers allow “TCP/UDP”)
   * **External port**: 8554    ⇐ higher ports less likely blocked (you can use 554 if you prefer)
   * **Internal IP**: `************`
   * **Internal port**: 554
3. **Save** and **restart** the router if necessary.

---

## 3. Register a DuckDNS Hostname

1. Go to **[https://www.duckdns.org/](https://www.duckdns.org/)** and sign up (via Google/GitHub—free).
2. Under **“add domain”**, pick a subdomain (e.g. `mycamera`) and click **“add domain”**.
3. You’ll see your **token** next to it—keep it handy.

---

## 4. Automate Your IP Updates

Since your home IP can change, have a small script call DuckDNS every few minutes:

### A. Windows PowerShell Script

Create a file `C:\duckdns\update.ps1`:

```powershell
$domain = "mycamera"
$token  = "YOUR_DUCKDNS_TOKEN"
Invoke-RestMethod -Uri "https://www.duckdns.org/update?domains=$domain&token=$token&ip="
```

### B. Schedule It

1. Open **Task Scheduler** → **Create Task…**
2. **General**: “DuckDNS IP Update”, run whether user is logged in.
3. **Triggers**: New → **Daily**, repeat task every **5 minutes** for **Indefinitely**.
4. **Actions**:

   * Action: **Start a program**
   * Program/script: `powershell.exe`
   * Add arguments: `-ExecutionPolicy Bypass -File "C:\duckdns\update.ps1"`
5. **OK**. You’ll now keep your DuckDNS record current.

---

## 5. Open Your Stream Remotely

* Your public endpoint is now:

  ```
  rtsp://<user>:<pass>@mycamera.duckdns.org:8554/stream0
  ```

  *(If your camera requires credentials, insert them before the host.)*

* **Test** in VLC or via `ping mycamera.duckdns.org` to confirm DNS resolution.

---

## 6. Use in Your Lambda AI Code

```python
import cv2

rtsp_url = "rtsp://mycamera.duckdns.org:8554/stream0"
cap = cv2.VideoCapture(rtsp_url)
if not cap.isOpened():
    raise RuntimeError("Failed to open RTSP stream")

while True:
    ret, frame = cap.read()
    if not ret:
        break
    # …your inference code…
```

---

# Lambda.ai Cloud Migration Guide: Integrated Weapon and Crowd Detection System

This guide provides step-by-step instructions for migrating the Integrated Weapon and Crowd Detection System to Lambda.ai Cloud.

## Prerequisites
- Lambda.ai account
- SSH client
- Git installed on your local machine

## Step 1: Create a Lambda.ai Instance
1. Log in to your Lambda.ai account
2. Create a new instance with your preferred specifications
3. Note the instance IP address for later use

## Step 2: Generate SSH Key for Secure Access
1. Navigate to the SSH Keys section in the Lambda.ai dashboard
2. Generate a new SSH key
3. Download and save the .pem file securely on your local host
4. Set the correct permissions for the key file:
   ```bash
   chmod 400 path/to/your-key.pem
   ```

# Run the below commands from your local host
## Step 3: setup the Lambda.ai instance IP address in a ENV variable
The IP Address is noted from the Step 1 above
```bash
export IP_ADD=104.171.203.XXX
```

## Step 4: Copy the setup_environment.sh file onto the Lambda.ai instance host
```bash
scp -i ~/.ssh/first_app.pem ~/code/utilities/setup_environment.sh ubuntu@$IP_ADD:/home/<USER>
```

## Step 5: Copy the lambda_ci private key shared secretly with you to the remote host
```bash
scp -p -i ~/.ssh/first_app.pem ~/.ssh/lambda_ci ubuntu@$IP_ADD:/home/<USER>
```

## Step 6: Login to the Lambda.ai SSH host using the SSH key file created in step 2
```bash
ssh -i ~/.ssh/first_app.pem ubuntu@$IP_ADD
```

# Run the below commands from your Lambda.ai host once you login

## Step 7: Change the permissions on the setup_environment.sh script
```bash
chmod +x setup_environment.sh
``` 

## Step 8: Setup the Deploy key to clone the protected github repository
```bash
export GIT_SSH_PRIVATE_KEY=$(base64 -w 0 ./lambda_ci)
```

## Step 9: Run the setup script
```bash
./setup_environment.sh
```

Note: Look for the below log on the console for a successful completion of the script installation
```bash
SUCCESS: Automation script completed all steps successfully!
```

## Step 10: Configure the Application
1. Copy the Example File
Duplicate the provided template file and rename it:
```bash cp config.ini.example config.ini
```

2. Open config.ini in Your Preferred Editor
Edit the file to set your own configuration values. For example:
```bash 
[paths]
bytetrack_path = C:\Users\<USER>\python_codes\admin-dashboard\ByteTrack
[huggingface]
token = <YOUR_HUGGINGFACE_TOKEN>
```
Replace C:\Users\<USER>\python_codes\admin-dashboard\ByteTrack with the actual path to your ByteTrack directory.
Replace <YOUR_HUGGINGFACE_TOKEN> with your personal HuggingFace access token.

3. Save the File
Ensure you save the file as config.ini in your project’s root directory.
Tip : Never commit your personal config.ini to version control. The config.ini.example should be tracked instead, so others can use it as a template for their own configuration files.


## Step 11: Access Jupyter Notebook
Create an SSH tunnel to access the Jupyter server:
```bash
ssh -i "path/to/your-key.pem" -L 8080:127.0.0.1:7000 ubuntu@your-instance-ip
```

Open `http://127.0.0.1:8080` in your browser. You'll be prompted for a token.

To find the Jupyter token, run this command in your Lambda.ai SSH terminal:
```bash
jupyter server list
```

Copy the token from the output and paste it into the browser prompt.

## Step 12: Run the Application
Activate the Virtual Env
```bash
source new_env/bin/activate
```
Open a terminal in the Jupyter interface and run:
```bash
python run.py
```

The application will start running on port 5000 on the Lambda instance.

## Step 13: Access the Admin Dashboard
Create another SSH tunnel to access the application:
```bash
ssh -i "path/to/your-key.pem" -L 4500:127.0.0.1:5000 ubuntu@your-instance-ip
```

Open `http://127.0.0.1:4500` in your browser to access the admin dashboard.

## Troubleshooting
- If you encounter connection issues, ensure your SSH key has the correct permissions (chmod 400)
- If the application doesn't start, check the logs for any dependency issues
- Make sure all required ports (7000, 5000) are available on the Lambda instance

## Additional Notes
- The default username for Lambda.ai instances is "ubuntu"
- Remember to terminate your instance when not in use to avoid unnecessary charges
- Consider setting up persistent storage if you need to preserve data between sessions



